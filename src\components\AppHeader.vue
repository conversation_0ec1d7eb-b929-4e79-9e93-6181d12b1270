<template>
  <header class="page-header">
    <div class="header-top">
      <div class="container">
        <div class="welcome-info">
          <i class="fas fa-home"></i>
          <span>歡迎來到數字起源 (香港) 有限公司！</span>
        </div>
        <div class="header-actions">
          <button class="lang-btn" @click="toggleLanguage">{{ currentLang }}</button>

          <button class="login-btn" @click="handleLogin">登錄/註冊</button>
        </div>
      </div>
    </div>
    
    <!-- 主导航 -->
    <nav class="main-nav">
      <div class="container">
        <ul class="nav-menu">
          <li class="nav-item current">
            <a href="/">首頁</a>
          </li>
          <li class="nav-item">
            <a href="/solutions">解決方案</a>
          </li>
          <li class="nav-item">
            <a href="/insights">行業洞察</a>
          </li>
          <li class="nav-item">
            <a href="/experience">體驗中心</a>
          </li>
          <li class="nav-item">
            <a href="/contact">聯繫我們</a>
          </li>
          <li class="nav-item">
            <a href="/careers">招賢納士</a>
          </li>
        </ul>
      </div>
    </nav>
  </header>
</template>

<script>
export default {
  name: 'AppHeader',
  data() {
    return {
      currentLang: 'EN'
    }
  },
  methods: {
    toggleLanguage() {
      this.currentLang = this.currentLang === 'EN' ? '中文' : 'EN'
      // 这里可以添加语言切换逻辑
      console.log('切换语言到:', this.currentLang)
    },

    handleLogin() {
      console.log('跳转到登录页面')
      // 这里可以添加登录逻辑
      this.$router.push('/login')
    }
  }
}
</script>

<style scoped>
/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面头部样式 */
.page-header {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-top {
  background: #f8f9fa;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.header-top .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #0064d8;
  font-size: 14px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.lang-btn {
  padding: 8px 12px;
  border: 1px solid #eee;
  background: #fff;
  color: #333;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.lang-btn:hover {
  background: #0064d8;
  color: #fff;
  border-color: #0064d8;
}



.login-btn {
  padding: 8px 16px;
  background: #0081f9;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.login-btn:hover {
  background: #0064d8;
}

/* 主导航样式 */
.main-nav {
  background: rgba(27, 28, 55, 1);
  padding: 0;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  justify-content: flex-end;
}

.nav-item {
  position: relative;
}

.nav-item > a {
  display: block;
  padding: 15px 20px;
  color: #fff;
  text-decoration: none;
  font-size: 16px;
  transition: all 0.3s;
}

.nav-item:hover > a,
.nav-item.current > a {
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
}



/* 响应式设计 */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    gap: 10px;
  }
  

  
  .nav-menu {
    flex-direction: column;
  }
}
</style>
