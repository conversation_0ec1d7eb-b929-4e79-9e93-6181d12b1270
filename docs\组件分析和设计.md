# 旧版官网组件详细分析

## 1. 页面结构分析

### 1.1 整体页面架构
基于对 `index.html` 的分析，页面采用以下结构：

```html
<div class="App loading">
  <div class="Page">
    <div class="Page-header">
      <!-- 头部内容 -->
    </div>
    <div class="Page-body">
      <!-- 主体内容 -->
    </div>
    <div class="Page-footer">
      <!-- 底部内容 -->
    </div>
  </div>
</div>
```

### 1.2 头部结构分析

#### 1.2.1 顶部栏组件 (Page-header--topbar)
**功能**: 显示欢迎信息和语言切换
**HTML结构**:
```html
<div class="Page-slot--template-top">
  <div class="cc-row">
    <div class="cc-col">
      <!-- 欢迎信息 -->
      <div class="cc-textblock">
        <p>歡迎來到數字起源 (香港) 有限公司！</p>
      </div>
    </div>
    <div class="cc-col">
      <!-- 语言切换、搜索、登录按钮 -->
    </div>
  </div>
</div>
```

**Vue 3组件设计**:
```vue
<template>
  <div class="top-bar">
    <div class="container">
      <div class="top-bar__content">
        <div class="welcome-message">
          <i class="fas fa-home"></i>
          {{ $t('welcome.message') }}
        </div>
        <div class="top-bar__actions">
          <LanguageSwitch />
          <SearchBox />
          <LoginButton />
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 1.2.2 主导航组件 (Page-header--main)
**功能**: 主要导航菜单
**特点**:
- 支持多级下拉菜单
- 响应式设计
- 悬停效果

**原始结构**:
```html
<div class="Page-header--main">
  <div class="Page-header--menu">
    <div class="cc-menudropdown">
      <div class="cc-menudropdown--nav">
        <div class="cc-menudropdown--item current">
          <a href="index.htm">首頁</a>
        </div>
        <div class="cc-menudropdown--item">
          <a href="智慧文旅.html">解決方案</a>
        </div>
        <!-- 更多菜单项 -->
      </div>
    </div>
  </div>
</div>
```

**Vue 3组件设计**:
```vue
<template>
  <nav class="main-navigation">
    <div class="container">
      <div class="nav-content">
        <AppLogo />
        <NavigationMenu :items="menuItems" />
        <div class="nav-actions">
          <SearchBox />
          <UserActions />
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
interface MenuItem {
  id: string;
  label: string;
  path?: string;
  children?: MenuItem[];
  external?: boolean;
}

const menuItems: MenuItem[] = [
  { id: 'home', label: '首頁', path: '/' },
  {
    id: 'solutions',
    label: '解決方案',
    children: [
      { id: 'smart-tourism', label: '智慧文旅', path: '/solutions/tourism' },
      { id: 'smart-city', label: '智慧城市', path: '/solutions/city' },
      { id: 'smart-ecology', label: '智慧生態', path: '/solutions/ecology' }
    ]
  },
  { id: 'insights', label: '行業洞察', path: '/insights' },
  { id: 'experience', label: '體驗中心', external: true, path: 'http://www.dataorigin.com.hk/shukeyun/' },
  { id: 'contact', label: '聯繫我們', path: '/contact' },
  { id: 'careers', label: '招賢納士', external: true, path: 'https://www.offertoday.com/hk/company/K_9_TTxML5dGyOKBV-VRFg==' }
];
</script>
```

## 2. 内容组件分析

### 2.1 布局组件系统

#### 2.1.1 行列布局 (cc-row / cc-col)
**功能**: 基于24栅格系统的响应式布局
**特点**:
- 支持Flexbox布局
- 响应式断点
- 对齐方式控制

**原始实现**:
```html
<div class="cc-row cc-row--flex cc-row--justify__start cc-row--align__top">
  <div class="cc-col cc-col-12 cc-col-xl-12 cc-col-lg-12 cc-col-md-12">
    <!-- 内容 -->
  </div>
  <div class="cc-col cc-col-12 cc-col-xl-12 cc-col-lg-12 cc-col-md-12">
    <!-- 内容 -->
  </div>
</div>
```

**Vue 3组件设计**:
```vue
<!-- AppRow.vue -->
<template>
  <div
    class="app-row"
    :class="[
      `justify-${justify}`,
      `align-${align}`,
      { 'no-gutters': noGutters }
    ]"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
interface RowProps {
  justify?: 'start' | 'center' | 'end' | 'space-between' | 'space-around';
  align?: 'top' | 'middle' | 'bottom' | 'stretch';
  noGutters?: boolean;
}

withDefaults(defineProps<RowProps>(), {
  justify: 'start',
  align: 'top',
  noGutters: false
});
</script>

<!-- AppCol.vue -->
<template>
  <div
    class="app-col"
    :class="[
      span && `col-${span}`,
      xs && `col-xs-${xs}`,
      sm && `col-sm-${sm}`,
      md && `col-md-${md}`,
      lg && `col-lg-${lg}`,
      xl && `col-xl-${xl}`
    ]"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
interface ColProps {
  span?: number;
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
}

defineProps<ColProps>();
</script>
```

### 2.2 内容组件

#### 2.2.1 文本块组件 (cc-textblock)
**功能**: 富文本内容展示
**特点**: 支持HTML内容、样式定制

**Vue 3组件设计**:
```vue
<template>
  <div class="text-block" :class="[size, align]">
    <div
      v-if="html"
      class="text-block__content richtext"
      v-html="content"
    />
    <div v-else class="text-block__content">
      <slot>{{ content }}</slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface TextBlockProps {
  content?: string;
  html?: boolean;
  size?: 'small' | 'medium' | 'large';
  align?: 'left' | 'center' | 'right' | 'justify';
}

withDefaults(defineProps<TextBlockProps>(), {
  html: false,
  size: 'medium',
  align: 'left'
});
</script>
```

#### 2.2.2 按钮组件 (cc-button)
**功能**: 可定制的按钮组件
**特点**: 多种样式、尺寸、状态

**Vue 3组件设计**:
```vue
<template>
  <component
    :is="tag"
    class="app-button"
    :class="[
      `button--${type}`,
      `button--${size}`,
      {
        'button--loading': loading,
        'button--disabled': disabled,
        'button--block': block
      }
    ]"
    :disabled="disabled || loading"
    :href="href"
    :target="target"
    @click="handleClick"
  >
    <i v-if="loading" class="button__loading"></i>
    <i v-else-if="icon" :class="icon" class="button__icon"></i>
    <span class="button__text">
      <slot>{{ text }}</slot>
    </span>
  </component>
</template>

<script setup lang="ts">
interface ButtonProps {
  type?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  text?: string;
  icon?: string;
  loading?: boolean;
  disabled?: boolean;
  block?: boolean;
  href?: string;
  target?: string;
}

const props = withDefaults(defineProps<ButtonProps>(), {
  type: 'primary',
  size: 'medium',
  loading: false,
  disabled: false,
  block: false
});

const emit = defineEmits<{
  click: [event: MouseEvent];
}>();

const tag = computed(() => props.href ? 'a' : 'button');

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event);
  }
};
</script>
```

#### 2.2.3 图片组件 (cc-picture)
**功能**: 响应式图片展示
**特点**: 懒加载、多种适配模式

**Vue 3组件设计**:
```vue
<template>
  <div class="app-picture" :class="[`align-${align}`, { clickable }]">
    <img
      ref="imageRef"
      :src="currentSrc"
      :alt="alt"
      :class="[`fit-${fit}`, { loaded: imageLoaded }]"
      @load="handleLoad"
      @error="handleError"
      @click="handleClick"
    />
    <div v-if="loading" class="picture__loading">
      <i class="loading-spinner"></i>
    </div>
  </div>
</template>

<script setup lang="ts">
interface PictureProps {
  src: string;
  alt?: string;
  fit?: 'cover' | 'contain' | 'fill' | 'scale-down';
  align?: 'left' | 'center' | 'right';
  lazy?: boolean;
  clickable?: boolean;
}

const props = withDefaults(defineProps<PictureProps>(), {
  alt: '',
  fit: 'cover',
  align: 'center',
  lazy: true,
  clickable: false
});

const emit = defineEmits<{
  click: [event: MouseEvent];
  load: [event: Event];
  error: [event: Event];
}>();

const imageRef = ref<HTMLImageElement>();
const imageLoaded = ref(false);
const loading = ref(true);
const currentSrc = ref('');

// 懒加载逻辑
const { stop } = useIntersectionObserver(
  imageRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting && props.lazy) {
      currentSrc.value = props.src;
      stop();
    }
  }
);

onMounted(() => {
  if (!props.lazy) {
    currentSrc.value = props.src;
  }
});

const handleLoad = (event: Event) => {
  imageLoaded.value = true;
  loading.value = false;
  emit('load', event);
};

const handleError = (event: Event) => {
  loading.value = false;
  emit('error', event);
};

const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event);
  }
};
</script>
```