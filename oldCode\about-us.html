﻿<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width,initial-scale=1" />
		<meta name="renderer" content="webkit" />
		<meta name="force-rendering" content="webkit" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta name="format-detection" content="telephone=no" />
		<meta name="generator" content="supercache" />
		<meta name="generated" content="1742381060" />
		<link
			rel="shortcut icon"
			type="image/x-icon"
			href="uploads/sites/1012/2022/11/09c91cee4d1de7c7f21689b82b9221c2.ico" />
		<title>Contact Us - 環球數科集團有限公司</title>
		<script
			class="reload-js"
			src="dist/visual/sites/1012/global.js?ver=1739755198397-12152"></script>
		<script id="_CONFIG_">
			window["_CONFIG_"]["sidebar"] = ""
			window["_CONFIG_"]["current"] = { module: "page", type: "page", id: 33 }
		</script>
		<link rel="stylesheet" href="dist/theme/static/css/core.css?ver=12152" />
		<link rel="stylesheet" href="dist/theme/static/css/main.css?ver=12152" />
		<link
			rel="stylesheet"
			href="dist/theme/static/css/main.media.css?ver=12152" />
		<script src="dist/theme/static/js/core.js?ver=12152"></script>
		<script src="dist/theme/static/js/main.js?ver=12152"></script>
		<link
			rel="stylesheet"
			class="reload-css"
			href="dist/visual/sites/1012/style.css?ver=1739755198397-12152" />

		<link rel="dns-prefetch" href="index.htm" />
		<link rel="dns-prefetch" href="//static2.xunxiang.site" />
		<link rel="canonical" href="%E8%81%94%E7%B3%BB%E6%88%91%E4%BB%AC.html" />

		<style class="custom-css-code">
			/* 陰影 */
			.custom-yy {
				box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
				-webkit-box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
				-moz-box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
			}
			/* 案例陰影 */
			.custom-al {
				box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
				-webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
				-moz-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
			}
		</style>
	</head>
	<body
		class="layout-full-width header-type-fixed header-type-mobile-default responsive">
		<div class="App loading">
			<div class="Page">
				<div class="Page-header">
					<div
						class="Page-slot--template-top"
						template_type="global"
						template_position="template-top"
						template_id="54">
						<div
							node-id="id-71-ompqcelcmx"
							node-type="row"
							class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__row">
							<style style-id="id-71-ompqcelcmx">
								@media only screen and (max-width: 767px) {
									[node-id="id-71-ompqcelcmx"] {
										border-bottom-color: #eeeeee;
										border-bottom-width: 1px;
										border-style: solid;
									}
								}
							</style>
							<script>
								;(function () {
									useComponent("row").default({
										id: "id-71-ompqcelcmx",
										options: {
											"full-width": "row",
											"adaption-height": "no",
											"background-video": "",
											"noheader-full-height": "no",
											"auto-flex": [],
											"auto-flex-enable": "no"
										}
									})
								})()
							</script>
							<div
								node-id="id-85-vzlyqjjsj0"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-12 cc-col-xl-12 cc-col-lg3-12 cc-col-lg2-12 cc-col-lg-12 cc-col-md-12 cc-col-sm-12 cc-col-xs-12">
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-85-vzlyqjjsj0",
											options: []
										})
									})()
								</script>
								<div class="cc-element--wrapper id-38-gg2lbbb6vg--wrapper">
									<div
										node-id="id-38-gg2lbbb6vg"
										node-type="block"
										class="cc-block cc-slot--wrapper">
										<script>
											;(function () {
												useComponent("block").default({
													id: "id-38-gg2lbbb6vg",
													options: []
												})
											})()
										</script>
										<div
											node-id="id-66-j6pl1uvvjh"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-66-j6pl1uvvjh",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-25-gzeawqw0td"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-0">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-25-gzeawqw0td",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-57-f1cjboih9d--wrapper">
													<style style-id="id-57-f1cjboih9d">
														@media only screen and (min-width: 768px) {
															[node-id="id-57-f1cjboih9d"] {
																padding-top: 10px;
																padding-right: 10px;
																padding-bottom: 10px;
																padding-left: 10px;
															}
														}
														[node-id="id-57-f1cjboih9d"] .cc-textblock__body {
															padding: 0px;
														}
													</style>
													<div
														node-id="id-57-f1cjboih9d"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p>
																<span style="color: #0064d8"
																	>&nbsp;<span class="mark-fapkw"
																		><!-- . --><span
																			contenteditable="false"
																			class="mark-fapk fas fa-home"></span
																	></span> </span
																>&nbsp;<span style="font-size: 14px"
																	>Welcome to Digital Origin (Hong Kong) Limited!</span
																>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-57-f1cjboih9d",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
										<div
											node-id="id-93-zii776eer7"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-93-zii776eer7",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-45-dplccc0zbv"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-0 cc-col-lg3-0 cc-col-lg2-0 cc-col-lg-0 cc-col-md-0 cc-col-sm-0 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-45-dplccc0zbv",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-65-txvzzlovo8--wrapper">
													<style style-id="id-65-txvzzlovo8">
														@media only screen and (min-width: 768px) {
															[node-id="id-65-txvzzlovo8"] {
																padding-top: 10px;
																padding-right: 10px;
																padding-bottom: 10px;
																padding-left: 10px;
															}
														}
														[node-id="id-65-txvzzlovo8"] .cc-textblock__body {
															padding: 10px;
														}
													</style>
													<div
														node-id="id-65-txvzzlovo8"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p>
																<span style="color: #0064d8"
																	> <span class="mark-fapkw"
																		><!-- . --><span
																			contenteditable="false"
																			class="mark-fapk fas fa-home"></span
																	></span> </span
																> <span style="font-size: 14px"
																	>歡迎來到環球數科！</span
																>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-65-txvzzlovo8",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div
								node-id="id-24-hm9hozmolg"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-12 cc-col-xl-12 cc-col-lg3-12 cc-col-lg2-12 cc-col-lg-12 cc-col-md-12 cc-col-sm-12 cc-col-xs-12">
								<style style-id="id-24-hm9hozmolg">
									@media only screen and (max-width: 767px) {
										[node-id="id-24-hm9hozmolg"] {
											padding-right: 10px;
										}
									}
									@media only screen and (min-width: 1360px) {
										[node-id="id-24-hm9hozmolg"] {
											padding-left: 100px;
										}
									}
								</style>
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-24-hm9hozmolg",
											options: []
										})
									})()
								</script>
								<div class="cc-element--wrapper id-62-zyiiii5rej--wrapper">
									<div
										node-id="id-62-zyiiii5rej"
										node-type="block"
										class="cc-block cc-slot--wrapper">
										<style style-id="id-62-zyiiii5rej">
											@media only screen and (max-width: 767px) {
												[node-id="id-62-zyiiii5rej"] {
													padding-bottom: 10px;
													padding-top: 10px;
												}
											}
										</style>
										<script>
											;(function () {
												useComponent("block").default({
													id: "id-62-zyiiii5rej",
													options: []
												})
											})()
										</script>
										<div
											node-id="id-61-o4kc47k5fb"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-61-o4kc47k5fb",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-12-w5d4dw8bbk"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-0">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-12-w5d4dw8bbk",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-45-lnnzwfqi5f--wrapper">
													<style style-id="id-45-lnnzwfqi5f">
														[node-id="id-45-lnnzwfqi5f"].cc-button {
															overflow: hidden;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button .slot-main {
															background-color: rgba(255, 255, 255, 1);
															color: #333333;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button
															.slot-main:hover {
															background-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button--line
															.slot-main {
															border-color: rgba(255, 255, 255, 1);
															color: #333333;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button--line
															.slot-main:hover {
															border-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button
															.slot-main
															table
															td {
															padding-left: 12px;
															padding-right: 12px;
															padding-top: 12px;
															padding-bottom: 12px;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button .slot-main {
															border: 1px solid #eeeeee;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button
															.slot-main:hover {
															border: 1px solid #0064d8;
														}
													</style>
													<div
														node-id="id-45-lnnzwfqi5f"
														node-type="button"
														class="cc-button cc-button-- cc-button--large cc-button--square cc-button--right">
														<div class="slot-main" id="id-45-lnnzwfqi5f">
															<p><span style="font-size: 14px">CN</span></p>
														</div>
													</div>
													<script>
														;(function () {
															useComponent("button").default({
																id: "id-45-lnnzwfqi5f",
																options: {
																	handler: {
																		action: "open",
																		options: { target: "_self", url: "\/index.htm" }
																	},
																	btnBorder: "yes",
																	"hover-animation": [],
																	align: "right",
																	size: "large"
																}
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-62-o1la61yy10"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-15 cc-col-xl-15 cc-col-lg3-15 cc-col-lg2-15 cc-col-lg-15 cc-col-md-15 cc-col-sm-15 cc-col-xs-0">
												<style style-id="id-62-o1la61yy10">
													@media only screen and (min-width: 768px) {
														[node-id="id-62-o1la61yy10"] {
															padding-right: 20px;
															padding-left: 20px;
														}
													}
												</style>
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-62-o1la61yy10",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-23-h94dm9wtdk--wrapper">
													<style style-id="id-23-h94dm9wtdk">
														[node-id="id-23-h94dm9wtdk"] {
															--border-color: var(--border-color);
															font-size: 14px;
														}
														[node-id="id-23-h94dm9wtdk"] .cc-search__field {
															color: #000;
														}
														[node-id="id-23-h94dm9wtdk"]
															.cc-search__field:hover,
														[node-id="id-23-h94dm9wtdk"]
															.cc-search__field:focus {
															border-color: var(--border-color);
															font-size: 14px;
														}
														[node-id="id-23-h94dm9wtdk"] .cc-search__field {
															background-color: ;
															height: 38px;
														}
														[node-id="id-23-h94dm9wtdk"] .choices__inner {
															height: 38px;
															line-height: 38px;
														}
														[node-id="id-23-h94dm9wtdk"] .cc-input-append {
															background-color: rgba(0, 129, 249, 1);
															line-height: 38px;
														}
														[node-id="id-23-h94dm9wtdk"] .cc-search--button {
															color: rgba(255, 255, 255, 1);
														}
														[node-id="id-23-h94dm9wtdk"]
															.cc-form--input__append
															.cc-input-append {
															padding: 0 10px;
														}
														[node-id="id-23-h94dm9wtdk"].cc-search
															.cc-input-append
															.cc-search--button
															img {
															vertical-align: middle;
														}
														[node-id="id-23-h94dm9wtdk"].cc-search
															.cc-input-append {
															line-height: 38px;
														}
														[node-id="id-23-h94dm9wtdk"].cc-search .cc-choices {
															min-width: 50px;
														}
													</style>
<!--													<div-->
<!--														node-id="id-23-h94dm9wtdk"-->
<!--														node-type="search"-->
<!--														class="cc-search cc-search&#45;&#45;width__full">-->
<!--														<div class="lay-fx">-->
<!--															<div-->
<!--																class="cc-form&#45;&#45;input__wrapper cc-form&#45;&#45;input__append">-->
<!--																<input-->
<!--																	class="cc-form&#45;&#45;input cc-search__field"-->
<!--																	type="text"-->
<!--																	placeholder="搜索文章、頁面" />-->
<!--																<div class="cc-input-append">-->
<!--																	<button-->
<!--																		class="cc-search&#45;&#45;button"-->
<!--																		action="https://www.hqshuke.com/search">-->
<!--																		<img-->
<!--																			src="uploads/sites/1012/2022/12/5981d2b1347e3df2eb51c919f1b7e57b.png"-->
<!--																			width="26"-->
<!--																			height="26" />-->
<!--																	</button>-->
<!--																</div>-->
<!--															</div>-->
<!--														</div>-->
<!--													</div>-->

													<script>
														;(function () {
															useComponent("search").default({
																id: "id-23-h94dm9wtdk",
																options: {
																	__note: "",
																	type: "page",
																	"select-min-width": "50px",
																	"select-ellipsis": "no",
																	open: "yes",
																	"width-full": "yes",
																	"placeholder-text":
																		"\u641c\u7d22\u6587\u7ae0\u3001\u9875\u9762",
																	"placeholder-text-color-enable": "no",
																	"placeholder-text-color": "#C2C2C2",
																	"show-search-icon": "yes",
																	"search-icon-src":
																		"\/\/static2.xunxiang.site\/uploads\/sites\/1012\/2022\/12\/5981d2b1347e3df2eb51c919f1b7e57b.png",
																	"btn-text": "\u641c\u7d22",
																	"btn-color": "rgba(255, 255, 255, 1)",
																	"btn-bg-color": "rgba(0, 129, 249, 1)",
																	"font-size": "14px",
																	"btn-pad": "10px",
																	height: "38px",
																	"border-color": "",
																	"bg-color": "",
																	"init-animation": [],
																	"hover-animation": [],
																	"keyword-required": "no",
																	"search-text-color": "#000",
																	"border-color-hover": "",
																	"select-style-enable": "no",
																	"select-style": [],
																	"present-hover-select-style": []
																}
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-73-me3uwghnqw"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-5 cc-col-xl-5 cc-col-lg3-5 cc-col-lg2-5 cc-col-lg-5 cc-col-md-5 cc-col-sm-5 cc-col-xs-0">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-73-me3uwghnqw",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-71-bj5bmz0t9e--wrapper">
													<style style-id="id-71-bj5bmz0t9e">
														[node-id="id-71-bj5bmz0t9e"].cc-button {
															overflow: hidden;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button .slot-main {
															background-color: rgba(0, 129, 249, 1);
															color: #ffffff;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button
															.slot-main:hover {
															background-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button--line
															.slot-main {
															border-color: rgba(0, 129, 249, 1);
															color: #ffffff;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button--line
															.slot-main:hover {
															border-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button
															.slot-main
															table
															td {
															padding-left: 12px;
															padding-right: 12px;
															padding-top: 12px;
															padding-bottom: 12px;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button .slot-main {
															border: 1px solid #ccc;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button
															.slot-main:hover {
															border: 1px solid #ccc;
														}
													</style>
													<div
														node-id="id-71-bj5bmz0t9e"
														node-type="button"
														class="cc-button cc-button-- cc-button--large cc-button--square cc-button--left">
														<div class="slot-main" id="id-71-bj5bmz0t9e">
															<p>
																<span style="font-size: 14px">Login/Register</span>
															</p>
														</div>
													</div>
													<script>
														;(function () {
															useComponent("button").default({
																id: "id-71-bj5bmz0t9e",
																options: {
																	handler: {
																		action: "open",
																		options: {
																			target: "_blank",
																			url: "https:\/\/prod.shukeyun.com\/cas\/login\/#\/login?appId=CvGwBHU2YiPygGGY5bMF"
																		}
																	},
																	btnBorder: "no",
																	"hover-animation": [],
																	align: "left",
																	size: "large"
																}
															})
														})()
													</script>
												</div>
											</div>
										</div>
										<div
											node-id="id-21-ki85qv186z"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-21-ki85qv186z",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-11-jx5af2kbms"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-8 cc-col-xl-0 cc-col-lg3-0 cc-col-lg2-0 cc-col-lg-0 cc-col-md-0 cc-col-sm-0 cc-col-xs-5">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-11-jx5af2kbms",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-18-egqdv8ysrr--wrapper">
													<style style-id="id-18-egqdv8ysrr">
														[node-id="id-18-egqdv8ysrr"] .cc-textblock__body {
															padding: 0px;
														}
														[node-id="id-18-egqdv8ysrr"].cc-textblock
															.cc-textblock__body,
														[node-id="id-18-egqdv8ysrr"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-18-egqdv8ysrr"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p style="text-align: center">
																<span style="font-size: 14px"
																	><a href="en.html">CN</a></span
																>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-18-egqdv8ysrr",
																options: []
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-61-pnaa7gq1ac"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-0 cc-col-lg3-0 cc-col-lg2-0 cc-col-lg-0 cc-col-md-0 cc-col-sm-0 cc-col-xs-6">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-61-pnaa7gq1ac",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-14-ckz0a4xh0k--wrapper">
													<div
														node-id="id-14-ckz0a4xh0k"
														node-type="picture"
														class="cc-picture cc-picture--align__right">
														<img
															class="cc-picture--img"
															src="uploads/sites/1012/2022/12/b9942aad9d1138e0bb16b5303ba9fba2.png" />
													</div>

													<script>
														;(function () {
															useComponent("picture").default({
																id: "id-14-ckz0a4xh0k",
																options: {
																	handler: {
																		action: "modal",
																		options: {
																			close: "1",
																			autoPlay: "0",
																			template_id: 3,
																			width: "center"
																		}
																	}
																}
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-62-v63z41dt68"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-0 cc-col-lg3-0 cc-col-lg2-0 cc-col-lg-0 cc-col-md-0 cc-col-sm-0 cc-col-xs-13">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-62-v63z41dt68",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-18-nita1s00y0--wrapper">
													<style style-id="id-18-nita1s00y0">
														[node-id="id-18-nita1s00y0"].cc-button {
															overflow: hidden;
														}
														[node-id="id-18-nita1s00y0"].cc-button .slot-main {
															background-color: rgba(0, 129, 249, 1);
															color: #ffffff;
														}
														[node-id="id-18-nita1s00y0"].cc-button
															.slot-main:hover {
															background-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-18-nita1s00y0"].cc-button--line
															.slot-main {
															border-color: rgba(0, 129, 249, 1);
															color: #ffffff;
														}
														[node-id="id-18-nita1s00y0"].cc-button--line
															.slot-main:hover {
															border-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-18-nita1s00y0"].cc-button
															.slot-main
															table
															td {
															padding-left: 12px;
															padding-right: 12px;
															padding-top: 12px;
															padding-bottom: 12px;
														}
														[node-id="id-18-nita1s00y0"].cc-button .slot-main {
															border: 1px solid #ccc;
														}
														[node-id="id-18-nita1s00y0"].cc-button
															.slot-main:hover {
															border: 1px solid #ccc;
														}
													</style>
													<div
														node-id="id-18-nita1s00y0"
														node-type="button"
														class="cc-button cc-button-- cc-button--large cc-button--square cc-button--right">
														<div class="slot-main" id="id-18-nita1s00y0">
															<p>Login/Register</p>
														</div>
													</div>
													<script>
														;(function () {
															useComponent("button").default({
																id: "id-18-nita1s00y0",
																options: {
																	handler: {
																		action: "open",
																		options: {
																			target: "_self",
																			url: "https:\/\/prod.shukeyun.com\/cas\/login\/#\/login?appId=CvGwBHU2YiPygGGY5bMF"
																		}
																	},
																	btnBorder: "no",
																	"hover-animation": [],
																	align: "right",
																	size: "large"
																}
															})
														})()
													</script>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="Page-header--main default">
						<div class="Page-header--main__in container">
							<div class="Page-header--custommenu">
								<div class="Page-header--menu">
									<div
										node-id="id-13-hewiycbx4p"
										node-type="row"
										class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
										<script>
											;(function () {
												useComponent("row").default({
													id: "id-13-hewiycbx4p",
													options: {
														"full-width": "default",
														"adaption-height": "no",
														"background-video": "",
														"noheader-full-height": "no",
														"auto-flex": [],
														"auto-flex-enable": "no"
													}
												})
											})()
										</script>
										<div
											node-id="id-51-g757bjwmff"
											node-type="column"
											class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
											<script>
												;(function () {
													useComponent("column").default({
														id: "id-51-g757bjwmff",
														options: []
													})
												})()
											</script>
											<div
												class="cc-element--wrapper id-60-nkt0mtoceo--wrapper">
												<div
													node-id="id-60-nkt0mtoceo"
													node-type="menudropdown"
													class="cc-menudropdown">
													<div class="cc-menudropdown--header">
														<div class="cc-menudropdown--nav">
															<div class="cc-menudropdown--item not-dropdown">
																<a
																	class="cc-menudropdown--link"
																	href="./en.html"
																	target="_self">
																	Front Page
																</a>
															</div>

															<div class="cc-menudropdown--item">
																<a
																	class="cc-menudropdown--link"
																	href="./smart-culturetourism.html"
																	target="_self">
																	Solution
																</a>
															</div>

															<div class="cc-menudropdown--item not-dropdown">
																<a
																	class="cc-menudropdown--link"
																	href="./industry-insights.html"
																	target="_self">
																	Industry Insights
																</a>
															</div>

															<div class="cc-menudropdown--item not-dropdown">
																<a
																	class="cc-menudropdown--link"
																	href="https://h292273.s806.ubn.cn/shukeyun/"
																	target="_self">
																	Experience Center
																</a>
															</div>
															<div class="cc-menudropdown--item">
																<a
																	class="cc-menudropdown--link"
																	href="./about-us.html"
																	target="_self">
																	Contact Us
																</a>
															</div>
															<div class="cc-menudropdown--item not-dropdown">
																<a
																	class="cc-menudropdown--link"
																	href="https://www.offertoday.com/hk/company/K_9_TTxML5dGyOKBV-VRFg=="
																	target="_self">
																	Careers
																</a>
															</div>
														</div>
													</div>
													<div class="cc-menudropdown--content">
														<style style-id="id-60-nkt0mtoceo">
															[node-id="id-60-nkt0mtoceo"] {
																--font-size: 16px;
															}
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--item {
																height: 30px;
																line-height: 30px;
																padding: 0 16px;
																background-color: rgba(255, 255, 255, 0);
															}
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--nav {
																text-align: right;
															}
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--item:hover,
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--item.current {
																background-color: rgba(255, 255, 255, 0);
															}
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--link {
																color: #ffffff;
															}
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--item:hover
																> .cc-menudropdown--link,
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--item.current
																> .cc-menudropdown--link {
																color: #1064a9;
															}
															[node-id="id-60-nkt0mtoceo"].cc-menudropdown
																.cc-menudropdown--content
																.cc-menudropdown--dropdown {
																top: calc(30px + 0px);
																border-radius: 0px;
															}
														</style>
														<!-- <script>
															;(function () {
																useComponent("menudropdown").default({
																	id: "id-60-nkt0mtoceo",
																	options: {
																		trigger: "hover",
																		"item-style": "default",
																		"line-style": "left",
																		"arrow-slide-enable": "no",
																		"item-padding": "16px",
																		dropup: "no"
																	}
																})
															})()
														</script> -->
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__menu"
															style="height: 300px; background: #fff">
															<div
																node-id="id-12-j8tbbm9j12g"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-12-j8tbbm9j12g",
																			options: {
																				"full-width": "default",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-11-0727ibql2p8"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-11-0727ibql2p8",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-10-etjn7sidor--wrapper">
																		<div
																			node-id="id-10-etjn7sidor"
																			node-type="placeholder"
																			class="cc-placeholder"
																			style="height: 50px"></div>

																		<script>
																			;(function () {
																				useComponent("placeholder").default({
																					id: "id-10-etjn7sidor",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="
																height: 430px;
																background: rgba(250, 250, 250, 1);
															">
															<div
																node-id="id-85-hfiq39ahcq"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__full">
																<style style-id="id-85-hfiq39ahcq">
																	[node-id="id-85-hfiq39ahcq"] {
																		background-color: rgba(229, 239, 251, 1);
																	}
																</style>
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-85-hfiq39ahcq",
																			options: {
																				"full-width": "full",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-97-jx99g23xdw"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-97-jx99g23xdw",
																				options: []
																			})
																		})()
																	</script>
																</div>
																<div
																	node-id="id-75-mzk3xbbfxw"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-75-mzk3xbbfxw",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-93-xtr08jgd1h--wrapper">
																		<div
																			node-id="id-93-xtr08jgd1h"
																			node-type="block"
																			class="cc-block cc-slot--wrapper">
																			<style style-id="id-93-xtr08jgd1h">
																				@media only screen and (min-width: 768px) {
																					[node-id="id-93-xtr08jgd1h"] {
																						padding-top: 30px;
																					}
																				}
																				[node-id="id-93-xtr08jgd1h"] {
																					height: 430px;
																					overflow: hidden;
																					overflow-y: auto;
																				}
																			</style>
																			<script>
																				;(function () {
																					useComponent("block").default({
																						id: "id-93-xtr08jgd1h",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				node-id="id-36-ib1yit3br3"
																				node-type="row"
																				class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																				<script>
																					;(function () {
																						useComponent("row").default({
																							id: "id-36-ib1yit3br3",
																							options: {
																								"full-width": "default",
																								"adaption-height": "no",
																								"background-video": "",
																								"noheader-full-height": "no",
																								"auto-flex": [],
																								"auto-flex-enable": "no"
																							}
																						})
																					})()
																				</script>
																				<div
																					node-id="id-54-fiy0nl2mnn"
																					node-type="column"
																					class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																					<script>
																						;(function () {
																							useComponent("column").default({
																								id: "id-54-fiy0nl2mnn",
																								options: []
																							})
																						})()
																					</script>
																					<div
																						class="cc-element--wrapper id-17-ch11kyzknp--wrapper">
																						<style style-id="id-17-ch11kyzknp">
																							[node-id="id-17-ch11kyzknp"]
																								.cc-textblock__body {
																								padding: 15px;
																							}
																							[node-id="id-17-ch11kyzknp"].cc-textblock
																								.cc-textblock__body,
																							[node-id="id-17-ch11kyzknp"].cc-textblock
																								.cc-textblock__body
																								* {
																								table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																								word-wrap: break-word; /*英文單字因自動換行*/
																								word-break: normal; /*正常避頭尾 */
																								text-align: justify; /*文字左右對齊*/
																								text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																							}
																						</style>
																						<div
																							node-id="id-17-ch11kyzknp"
																							node-type="textblock"
																							class="cc-textblock">
																							<div
																								class="cc-textblock__body richtext">
																								<p
																									class="font-64361d5afc91249e3bd51e624b693b37">
																									關於我們
																								</p>
																							</div>
																						</div>

																						<script>
																							;(function () {
																								useComponent(
																									"textblock"
																								).default({
																									id: "id-17-ch11kyzknp",
																									options: []
																								})
																							})()
																						</script>
																					</div>
																					<div
																						class="cc-element--wrapper id-45-c8m00gewhh--wrapper">
																						<style style-id="id-45-c8m00gewhh">
																							[node-id="id-45-c8m00gewhh"].cc-menu.cc-menu--vertical
																								.cc-menu--nav
																								.cc-menu--item {
																								box-sizing: border-box;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.line_box {
																								background: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.cc-menu--item.current
																								> .line_box {
																								width: 100%;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								li.menu {
																								color: #000000;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--nav
																								> .cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--horizontal {
																								text-align: left;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--horizontal
																								> .cc-menu--nav
																								> .cc-menu--item {
																								height: 40px;
																								line-height: 40px;
																								padding: 0 20px;
																								margin: 0px 0px;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--vertical
																								> .cc-menu--nav
																								> .cc-menu--item {
																								margin: 0px 0;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								height: 40px;
																								line-height: 40px;
																								margin: 0px 0;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--vertical
																								.cc-menu--item {
																								line-height: 40px;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								> .cc-menu--nav
																								> .cc-menu--item {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item.block,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item.current,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item:hover {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-45-c8m00gewhh"] {
																								font-size: 16px;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.cc-menu--item.block,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.cc-menu--item.current,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.cc-menu--item:hover {
																								border-bottom-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item__link {
																								color: #333333;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item.active
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item {
																								background-color: #00b5ae;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover {
																								background-color: #009892;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								color: #fff;
																								text-align: left;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link {
																								color: #fff;
																								text-align: left;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #fff;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: flex-start;
																								text-align: left;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: space-between;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item__link
																								> i {
																								width: 30px;
																								transform: rotate(-90deg);
																								transform-origin: 50% 50%;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--auto
																								> .cc-menu--nav {
																								font-size: 16px;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #fff;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu.cc-menu--auto__mini
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-45-c8m00gewhh"].cc-menu--auto
																									.cc-menu--expand__header {
																									display: block;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--auto
																									> .cc-menu--nav {
																									display: none;
																									opacity: 0; /*position: fixed;*/
																									position: relative;
																									z-index: 25;
																									width: 100%;
																									left: 0;
																									top: 50px;
																									height: calc(100% - 50px);
																									padding: 0 10px;
																									box-sizing: border-box;
																									overflow: hidden;
																									overflow-y: auto;
																								}
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--nav
																									> .cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--horizontal
																									> .cc-menu--nav
																									> .cc-menu--item {
																									height: 60px;
																									line-height: 60px;
																									padding: 0 20px;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--vertical
																									> .cc-menu--nav
																									> .cc-menu--item {
																									margin: 20px 0;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--vertical
																									.cc-menu--item {
																									line-height: 60px;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item {
																									background-color: #fff;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item.block,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item.current,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item:hover {
																									background-color: #fff;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																									.cc-menu--item.block,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																									.cc-menu--item.current,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																									.cc-menu--item:hover {
																									border-bottom-color: #fff;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item__link {
																									color: #3c3c3c;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item:hover {
																									background-color: #009892;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu.cc-menu--auto__mini
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									height: 43px;
																									line-height: 43px;
																									margin: 0px 0;
																								}
																							}
																							@media only screen and (min-width: 767px) {
																							}
																						</style>
																						<div
																							node-id="id-45-c8m00gewhh"
																							node-type="menu"
																							class="cc-menu cc-menu--style__default cc-menu--vertical cc-menu--arrow-icon cc-menu--line-main">
																							<ul class="cc-menu--nav">
																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E5%85%B3%E4%BA%8E%E6%88%91%E4%BB%AC.html#公司簡介">
																											<span
																												class="cc-menu--item__title">
																												公司簡介
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E5%85%B3%E4%BA%8E%E6%88%91%E4%BB%AC.html#業務佈局">
																											<span
																												class="cc-menu--item__title">
																												業務佈局
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E5%85%B3%E4%BA%8E%E6%88%91%E4%BB%AC.html#榮譽資質">
																											<span
																												class="cc-menu--item__title">
																												榮譽資質
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E5%85%B3%E4%BA%8E%E6%88%91%E4%BB%AC.html#企業文化">
																											<span
																												class="cc-menu--item__title">
																												企業文化
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E5%85%B3%E4%BA%8E%E6%88%91%E4%BB%AC.html#發展歷程">
																											<span
																												class="cc-menu--item__title">
																												發展歷程
																											</span>
																										</a>
																									</div>
																								</li>
																							</ul>
																						</div>

																						<script>
																							;(function () {
																								useComponent("menu").default({
																									id: "id-45-c8m00gewhh",
																									options: {
																										hover_show: "no",
																										show_cur_sub: "no",
																										retain_hover: "none",
																										"line-style-obj":
																											"main_menu",
																										"line-style": "left",
																										mode: "vertical",
																										style: "default",
																										"menu-item-repulsion": "no"
																									}
																								})
																							})()
																						</script>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	node-id="id-42-xnxqb4aurw"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-42-xnxqb4aurw",
																				options: []
																			})
																		})()
																	</script>
																</div>
																<div
																	node-id="id-84-zcv04v0v34"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-8 cc-col-lg3-8 cc-col-lg2-8 cc-col-lg-8 cc-col-md-8 cc-col-sm-8 cc-col-xs-24">
																	<style style-id="id-84-zcv04v0v34">
																		[node-id="id-84-zcv04v0v34"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																		@media only screen and (min-width: 768px) {
																			[node-id="id-84-zcv04v0v34"] {
																				padding-top: 50px;
																				padding-bottom: 80px;
																				padding-left: 50px;
																			}
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-84-zcv04v0v34",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-37-hikpu6411f--wrapper">
																		<style style-id="id-37-hikpu6411f">
																			[node-id="id-37-hikpu6411f"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-37-hikpu6411f"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-37-hikpu6411f"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-37-hikpu6411f"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					瞭解<span style="color: #0064d8"
																						>環球數科</span
																					>
																				</p>
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					核心產品！
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-37-hikpu6411f",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																	<div
																		class="cc-element--wrapper id-20-bcpagusgsl--wrapper">
																		<style style-id="id-20-bcpagusgsl">
																			[node-id="id-20-bcpagusgsl"] {
																				padding-top: 40px;
																			}
																			[node-id="id-20-bcpagusgsl"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-20-bcpagusgsl"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-20-bcpagusgsl"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-20-bcpagusgsl"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p>
																					&ldquo;3+X&rdquo;業務佈局：
																					<a
																						href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85.html"
																						>智慧文旅</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a
																						href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82.html?vc_preview=yes"
																						>智慧城市</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a
																						href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81.html"
																						>智慧生態</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a href="/通用解決方案">更多行業</a>
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-20-bcpagusgsl",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
																<div
																	node-id="id-95-xggqlyl0zz"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<style style-id="id-95-xggqlyl0zz">
																		[node-id="id-95-xggqlyl0zz"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-95-xggqlyl0zz",
																				options: []
																			})
																		})()
																	</script>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="
																height: 430px;
																background: rgba(250, 250, 250, 1);
															">
															<div
																node-id="id-26-zwtg0k9kue"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__full">
																<style style-id="id-26-zwtg0k9kue">
																	[node-id="id-26-zwtg0k9kue"] {
																		background-color: rgba(229, 239, 251, 1);
																	}
																</style>
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-26-zwtg0k9kue",
																			options: {
																				"full-width": "full",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-59-s3kf2yfb2k"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-59-s3kf2yfb2k",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-23-d7utcvzoho--wrapper">
																		<div
																			node-id="id-23-d7utcvzoho"
																			node-type="block"
																			class="cc-block cc-slot--wrapper">
																			<style style-id="id-23-d7utcvzoho">
																				@media only screen and (min-width: 768px) {
																					[node-id="id-23-d7utcvzoho"] {
																						padding-top: 30px;
																					}
																				}
																				[node-id="id-23-d7utcvzoho"] {
																					height: 430px;
																					overflow: hidden;
																					overflow-y: auto;
																				}
																			</style>
																			<script>
																				;(function () {
																					useComponent("block").default({
																						id: "id-23-d7utcvzoho",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				node-id="id-21-n22ax2ejax"
																				node-type="row"
																				class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																				<script>
																					;(function () {
																						useComponent("row").default({
																							id: "id-21-n22ax2ejax",
																							options: {
																								"full-width": "default",
																								"adaption-height": "no",
																								"background-video": "",
																								"noheader-full-height": "no",
																								"auto-flex": [],
																								"auto-flex-enable": "no"
																							}
																						})
																					})()
																				</script>
																				<div
																					node-id="id-37-kauo8fgndx"
																					node-type="column"
																					class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																					<script>
																						;(function () {
																							useComponent("column").default({
																								id: "id-37-kauo8fgndx",
																								options: []
																							})
																						})()
																					</script>
																					<div
																						class="cc-element--wrapper id-82-nln8wu1qzq--wrapper">
																						<style style-id="id-82-nln8wu1qzq">
																							[node-id="id-82-nln8wu1qzq"]
																								.cc-textblock__body {
																								padding: 15px;
																							}
																							[node-id="id-82-nln8wu1qzq"].cc-textblock
																								.cc-textblock__body,
																							[node-id="id-82-nln8wu1qzq"].cc-textblock
																								.cc-textblock__body
																								* {
																								table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																								word-wrap: break-word; /*英文單字因自動換行*/
																								word-break: normal; /*正常避頭尾 */
																								text-align: justify; /*文字左右對齊*/
																								text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																							}
																						</style>
																						<div
																							node-id="id-82-nln8wu1qzq"
																							node-type="textblock"
																							class="cc-textblock">
																							<div
																								class="cc-textblock__body richtext">
																								<p
																									class="font-64361d5afc91249e3bd51e624b693b37">
																									行業解決方案
																								</p>
																							</div>
																						</div>

																						<script>
																							;(function () {
																								useComponent(
																									"textblock"
																								).default({
																									id: "id-82-nln8wu1qzq",
																									options: []
																								})
																							})()
																						</script>
																					</div>
																					<div
																						class="cc-element--wrapper id-55-ttx2bdc15t--wrapper">
																						<style style-id="id-55-ttx2bdc15t">
																							[node-id="id-55-ttx2bdc15t"].cc-menu.cc-menu--vertical
																								.cc-menu--nav
																								.cc-menu--item {
																								box-sizing: border-box;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.line_box {
																								background: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.cc-menu--item.current
																								> .line_box {
																								width: 100%;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								li.menu {
																								color: #000000;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--nav
																								> .cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--horizontal {
																								text-align: left;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--horizontal
																								> .cc-menu--nav
																								> .cc-menu--item {
																								height: 40px;
																								line-height: 40px;
																								padding: 0 20px;
																								margin: 0px 0px;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--vertical
																								> .cc-menu--nav
																								> .cc-menu--item {
																								margin: 0px 0;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								height: 40px;
																								line-height: 40px;
																								margin: 0px 0;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--vertical
																								.cc-menu--item {
																								line-height: 40px;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								> .cc-menu--nav
																								> .cc-menu--item {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item.block,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item.current,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item:hover {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"] {
																								font-size: 16px;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.cc-menu--item.block,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.cc-menu--item.current,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.cc-menu--item:hover {
																								border-bottom-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item__link {
																								color: #333333;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item.active
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item {
																								background-color: rgba(
																									0,
																									181,
																									174,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover {
																								background-color: rgba(
																									0,
																									152,
																									146,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								color: #666666;
																								text-align: left;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link {
																								color: #0064d8;
																								text-align: left;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: flex-start;
																								text-align: left;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: space-between;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item__link
																								> i {
																								width: 30px;
																								transform: rotate(-90deg);
																								transform-origin: 50% 50%;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--auto
																								> .cc-menu--nav {
																								font-size: 16px;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu.cc-menu--auto__mini
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-55-ttx2bdc15t"].cc-menu--auto
																									.cc-menu--expand__header {
																									display: block;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--auto
																									> .cc-menu--nav {
																									display: none;
																									opacity: 0; /*position: fixed;*/
																									position: relative;
																									z-index: 25;
																									width: 100%;
																									left: 0;
																									top: 50px;
																									height: calc(100% - 50px);
																									padding: 0 10px;
																									box-sizing: border-box;
																									overflow: hidden;
																									overflow-y: auto;
																								}
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--nav
																									> .cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--horizontal
																									> .cc-menu--nav
																									> .cc-menu--item {
																									height: 60px;
																									line-height: 60px;
																									padding: 0 20px;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--vertical
																									> .cc-menu--nav
																									> .cc-menu--item {
																									margin: 20px 0;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--vertical
																									.cc-menu--item {
																									line-height: 60px;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item {
																									background-color: #fff;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item.block,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item.current,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item:hover {
																									background-color: #fff;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																									.cc-menu--item.block,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																									.cc-menu--item.current,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																									.cc-menu--item:hover {
																									border-bottom-color: #fff;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item__link {
																									color: #3c3c3c;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item:hover {
																									background-color: #009892;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu.cc-menu--auto__mini
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									height: 43px;
																									line-height: 43px;
																									margin: 0px 0;
																								}
																							}
																							@media only screen and (min-width: 767px) {
																							}
																						</style>
																						<div
																							node-id="id-55-ttx2bdc15t"
																							node-type="menu"
																							class="cc-menu cc-menu--style__default cc-menu--vertical cc-menu--arrow-icon cc-menu--line-main">
																							<ul class="cc-menu--nav">
																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85.html">
																											<span
																												class="cc-menu--item__title">
																												智慧文旅
																											</span>
																										</a>
																										<i
																											class="fas fa-caret-down down-icon"></i>
																									</div>
																									<ul class="cc-menu--nav">
																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85/%E6%95%B0%E5%AD%97%E6%99%AF%E5%8C%BA.html">
																													<span
																														class="cc-menu--item__title">
																														數字景區
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85/%E6%99%BA%E8%83%BD%E8%90%A5%E9%94%80.html">
																													<span
																														class="cc-menu--item__title">
																														智能營銷
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85/%E6%99%BA%E6%85%A7%E7%9B%91%E7%AE%A1.html">
																													<span
																														class="cc-menu--item__title">
																														智慧監管
																													</span>
																												</a>
																											</div>
																										</li>
																									</ul>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82-1.html">
																											<span
																												class="cc-menu--item__title">
																												城市服務
																											</span>
																										</a>
																										<i
																											class="fas fa-caret-down down-icon"></i>
																									</div>
																									<ul class="cc-menu--nav">
																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82/%E6%99%BA%E6%85%A7%E7%A4%BE%E5%8C%BA.html">
																													<span
																														class="cc-menu--item__title">
																														智慧社區
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82/%E6%99%BA%E6%85%A7%E5%85%AC%E4%BA%A4.html">
																													<span
																														class="cc-menu--item__title">
																														智慧公交
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82/%E6%99%BA%E6%85%A7%E5%9C%BA%E7%AB%99.html">
																													<span
																														class="cc-menu--item__title">
																														智慧場站
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82/%E5%9F%8E%E5%AE%89%E5%BA%94%E6%80%A5.html">
																													<span
																														class="cc-menu--item__title">
																														城安應急
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82/%E6%99%BA%E6%85%A7%E5%85%85%E7%94%B5%E6%A1%A9.html">
																													<span
																														class="cc-menu--item__title">
																														智慧充電樁
																													</span>
																												</a>
																											</div>
																										</li>
																									</ul>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81.html">
																											<span
																												class="cc-menu--item__title">
																												智慧生態
																											</span>
																										</a>
																										<i
																											class="fas fa-caret-down down-icon"></i>
																									</div>
																									<ul class="cc-menu--nav">
																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81/%E7%94%9F%E6%80%81%E7%9B%91%E6%B5%8B.html">
																													<span
																														class="cc-menu--item__title">
																														生態監測
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81/%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1.html">
																													<span
																														class="cc-menu--item__title">
																														災害預警
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81/%E6%A3%AE%E6%9E%97%E9%98%B2%E7%81%AB.html">
																													<span
																														class="cc-menu--item__title">
																														森林防火
																													</span>
																												</a>
																											</div>
																										</li>
																									</ul>
																								</li>
																							</ul>
																						</div>

																						<script>
																							;(function () {
																								useComponent("menu").default({
																									id: "id-55-ttx2bdc15t",
																									options: {
																										hover_show: "no",
																										show_cur_sub: "yes",
																										retain_hover: "none",
																										"line-style-obj":
																											"main_menu",
																										"line-style": "left",
																										mode: "vertical",
																										style: "default",
																										"menu-item-repulsion": "no"
																									}
																								})
																							})()
																						</script>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	node-id="id-51-jznnd6t0u6"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-51-jznnd6t0u6",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-5-50p7sikk6a--wrapper">
																		<div
																			node-id="id-5-50p7sikk6a"
																			node-type="block"
																			class="cc-block cc-slot--wrapper">
																			<style style-id="id-5-50p7sikk6a">
																				@media only screen and (min-width: 768px) {
																					[node-id="id-5-50p7sikk6a"] {
																						padding-top: 30px;
																					}
																				}
																				[node-id="id-5-50p7sikk6a"] {
																					height: 430px;
																					overflow: hidden;
																					overflow-y: auto;
																				}
																			</style>
																			<script>
																				;(function () {
																					useComponent("block").default({
																						id: "id-5-50p7sikk6a",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				node-id="id-6-qb7gcin1mn"
																				node-type="row"
																				class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																				<script>
																					;(function () {
																						useComponent("row").default({
																							id: "id-6-qb7gcin1mn",
																							options: {
																								"full-width": "default",
																								"adaption-height": "no",
																								"background-video": "",
																								"noheader-full-height": "no",
																								"auto-flex": [],
																								"auto-flex-enable": "no"
																							}
																						})
																					})()
																				</script>
																				<div
																					node-id="id-7-k0b79demsfo"
																					node-type="column"
																					class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																					<script>
																						;(function () {
																							useComponent("column").default({
																								id: "id-7-k0b79demsfo",
																								options: []
																							})
																						})()
																					</script>
																					<div
																						class="cc-element--wrapper id-8-346k1lt82r8--wrapper">
																						<style style-id="id-8-346k1lt82r8">
																							[node-id="id-8-346k1lt82r8"]
																								.cc-textblock__body {
																								padding: 15px;
																							}
																							[node-id="id-8-346k1lt82r8"].cc-textblock
																								.cc-textblock__body,
																							[node-id="id-8-346k1lt82r8"].cc-textblock
																								.cc-textblock__body
																								* {
																								table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																								word-wrap: break-word; /*英文單字因自動換行*/
																								word-break: normal; /*正常避頭尾 */
																								text-align: justify; /*文字左右對齊*/
																								text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																							}
																						</style>
																						<div
																							node-id="id-8-346k1lt82r8"
																							node-type="textblock"
																							class="cc-textblock">
																							<div
																								class="cc-textblock__body richtext">
																								<p
																									class="font-64361d5afc91249e3bd51e624b693b37">
																									通用解決方案
																								</p>
																							</div>
																						</div>

																						<script>
																							;(function () {
																								useComponent(
																									"textblock"
																								).default({
																									id: "id-8-346k1lt82r8",
																									options: []
																								})
																							})()
																						</script>
																					</div>
																					<div
																						class="cc-element--wrapper id-9-5kfgltlusq--wrapper">
																						<style style-id="id-9-5kfgltlusq">
																							[node-id="id-9-5kfgltlusq"].cc-menu.cc-menu--vertical
																								.cc-menu--nav
																								.cc-menu--item {
																								box-sizing: border-box;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.line_box {
																								background: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.cc-menu--item.current
																								> .line_box {
																								width: 100%;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								li.menu {
																								color: #000000;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--nav
																								> .cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--horizontal {
																								text-align: left;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--horizontal
																								> .cc-menu--nav
																								> .cc-menu--item {
																								height: 40px;
																								line-height: 40px;
																								padding: 0 20px;
																								margin: 0px 0px;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--vertical
																								> .cc-menu--nav
																								> .cc-menu--item {
																								margin: 0px 0;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								height: 40px;
																								line-height: 40px;
																								margin: 0px 0;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--vertical
																								.cc-menu--item {
																								line-height: 40px;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								> .cc-menu--nav
																								> .cc-menu--item {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item.block,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item.current,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item:hover {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"] {
																								font-size: 16px;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.cc-menu--item.block,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.cc-menu--item.current,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.cc-menu--item:hover {
																								border-bottom-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item__link {
																								color: #333333;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item.active
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item {
																								background-color: rgba(
																									0,
																									181,
																									174,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover {
																								background-color: rgba(
																									0,
																									152,
																									146,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								color: #666666;
																								text-align: left;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link {
																								color: #0064d8;
																								text-align: left;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: flex-start;
																								text-align: left;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: space-between;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item__link
																								> i {
																								width: 30px;
																								transform: rotate(-90deg);
																								transform-origin: 50% 50%;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--auto
																								> .cc-menu--nav {
																								font-size: 16px;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu.cc-menu--auto__mini
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-9-5kfgltlusq"].cc-menu--auto
																									.cc-menu--expand__header {
																									display: block;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--auto
																									> .cc-menu--nav {
																									display: none;
																									opacity: 0; /*position: fixed;*/
																									position: relative;
																									z-index: 25;
																									width: 100%;
																									left: 0;
																									top: 50px;
																									height: calc(100% - 50px);
																									padding: 0 10px;
																									box-sizing: border-box;
																									overflow: hidden;
																									overflow-y: auto;
																								}
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--nav
																									> .cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--horizontal
																									> .cc-menu--nav
																									> .cc-menu--item {
																									height: 60px;
																									line-height: 60px;
																									padding: 0 20px;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--vertical
																									> .cc-menu--nav
																									> .cc-menu--item {
																									margin: 20px 0;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--vertical
																									.cc-menu--item {
																									line-height: 60px;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item {
																									background-color: #fff;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item.block,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item.current,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item:hover {
																									background-color: #fff;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																									.cc-menu--item.block,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																									.cc-menu--item.current,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																									.cc-menu--item:hover {
																									border-bottom-color: #fff;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item__link {
																									color: #3c3c3c;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item:hover {
																									background-color: #009892;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu.cc-menu--auto__mini
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									height: 43px;
																									line-height: 43px;
																									margin: 0px 0;
																								}
																							}
																							@media only screen and (min-width: 767px) {
																							}
																						</style>
																						<div
																							node-id="id-9-5kfgltlusq"
																							node-type="menu"
																							class="cc-menu cc-menu--style__default cc-menu--vertical cc-menu--arrow-icon cc-menu--line-main">
																							<ul class="cc-menu--nav">
																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%95%B0%E9%87%91%E4%BA%91-copy.html">
																											<span
																												class="cc-menu--item__title">
																												數金雲
																											</span>
																										</a>
																										<i
																											class="fas fa-caret-down down-icon"></i>
																									</div>
																									<ul class="cc-menu--nav">
																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E9%87%91%E4%BA%91-copy/%E4%BA%A4%E6%98%93%E7%BB%93%E7%AE%97.html">
																													<span
																														class="cc-menu--item__title">
																														交易結算
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E9%87%91%E4%BA%91-copy/%E6%95%B0%E5%AD%97%E6%94%AF%E4%BB%98.html">
																													<span
																														class="cc-menu--item__title">
																														數字支付
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E9%87%91%E4%BA%91-copy/%E8%B7%A8%E5%A2%83%E6%B1%87%E6%AC%BE.html">
																													<span
																														class="cc-menu--item__title">
																														跨境匯款
																													</span>
																												</a>
																											</div>
																										</li>
																									</ul>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%95%B0%E7%A7%91%E4%BA%91.html">
																											<span
																												class="cc-menu--item__title">
																												數科雲
																											</span>
																										</a>
																										<i
																											class="fas fa-caret-down down-icon"></i>
																									</div>
																									<ul class="cc-menu--nav">
																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E7%A7%91%E4%BA%91/%E8%BA%AB%E4%BB%BD%E8%AE%A4%E8%AF%81.html">
																													<span
																														class="cc-menu--item__title">
																														身份認證
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E7%A7%91%E4%BA%91/%E6%95%B0%E6%8D%AE%E6%B2%BB%E7%90%86.html">
																													<span
																														class="cc-menu--item__title">
																														數據治理
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E7%A7%91%E4%BA%91/%E7%AE%97%E6%B3%95%E6%A8%A1%E5%9E%8B.html">
																													<span
																														class="cc-menu--item__title">
																														算法模型
																													</span>
																												</a>
																											</div>
																										</li>
																									</ul>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%95%B0%E9%87%91%E4%BA%91.html">
																											<span
																												class="cc-menu--item__title">
																												區塊鏈應用
																											</span>
																										</a>
																									</div>
																								</li>
																							</ul>
																						</div>

																						<script>
																							;(function () {
																								useComponent("menu").default({
																									id: "id-9-5kfgltlusq",
																									options: {
																										hover_show: "no",
																										show_cur_sub: "yes",
																										retain_hover: "none",
																										"line-style-obj":
																											"main_menu",
																										"line-style": "left",
																										mode: "vertical",
																										style: "default",
																										"menu-item-repulsion": "no"
																									}
																								})
																							})()
																						</script>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	node-id="id-60-zz8x9v5o2r"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-60-zz8x9v5o2r",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-76-kzlmotmh3o--wrapper">
																		<div
																			node-id="id-76-kzlmotmh3o"
																			node-type="block"
																			class="cc-block cc-slot--wrapper">
																			<style style-id="id-76-kzlmotmh3o">
																				@media only screen and (min-width: 768px) {
																					[node-id="id-76-kzlmotmh3o"] {
																						padding-top: 30px;
																					}
																				}
																				[node-id="id-76-kzlmotmh3o"] {
																					height: 430px;
																					overflow: hidden;
																					overflow-y: auto;
																				}
																			</style>
																			<script>
																				;(function () {
																					useComponent("block").default({
																						id: "id-76-kzlmotmh3o",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				node-id="id-75-mo3ppnz0vz"
																				node-type="row"
																				class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																				<script>
																					;(function () {
																						useComponent("row").default({
																							id: "id-75-mo3ppnz0vz",
																							options: {
																								"full-width": "default",
																								"adaption-height": "no",
																								"background-video": "",
																								"noheader-full-height": "no",
																								"auto-flex": [],
																								"auto-flex-enable": "no"
																							}
																						})
																					})()
																				</script>
																				<div
																					node-id="id-57-s1prxoo2go"
																					node-type="column"
																					class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																					<script>
																						;(function () {
																							useComponent("column").default({
																								id: "id-57-s1prxoo2go",
																								options: []
																							})
																						})()
																					</script>
																					<div
																						class="cc-element--wrapper id-81-lnvd0fgv2z--wrapper">
																						<style style-id="id-81-lnvd0fgv2z">
																							[node-id="id-81-lnvd0fgv2z"]
																								.cc-textblock__body {
																								padding: 15px;
																							}
																							[node-id="id-81-lnvd0fgv2z"].cc-textblock
																								.cc-textblock__body,
																							[node-id="id-81-lnvd0fgv2z"].cc-textblock
																								.cc-textblock__body
																								* {
																								table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																								word-wrap: break-word; /*英文單字因自動換行*/
																								word-break: normal; /*正常避頭尾 */
																								text-align: justify; /*文字左右對齊*/
																								text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																							}
																						</style>
																						<div
																							node-id="id-81-lnvd0fgv2z"
																							node-type="textblock"
																							class="cc-textblock">
																							<div
																								class="cc-textblock__body richtext">
																								<p
																									class="font-64361d5afc91249e3bd51e624b693b37">
																									AI Solution
																								</p>
																							</div>
																						</div>

																						<script>
																							;(function () {
																								useComponent(
																									"textblock"
																								).default({
																									id: "id-81-lnvd0fgv2z",
																									options: []
																								})
																							})()
																						</script>
																					</div>
																					<div
																						class="cc-element--wrapper id-66-eizdndxdbx--wrapper">
																						<style style-id="id-66-eizdndxdbx">
																							[node-id="id-66-eizdndxdbx"].cc-menu.cc-menu--vertical
																								.cc-menu--nav
																								.cc-menu--item {
																								box-sizing: border-box;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.line_box {
																								background: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.cc-menu--item.current
																								> .line_box {
																								width: 100%;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								li.menu {
																								color: #000000;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--nav
																								> .cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--horizontal {
																								text-align: left;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--horizontal
																								> .cc-menu--nav
																								> .cc-menu--item {
																								height: 40px;
																								line-height: 40px;
																								padding: 0 20px;
																								margin: 0px 0px;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--vertical
																								> .cc-menu--nav
																								> .cc-menu--item {
																								margin: 0px 0;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								height: 40px;
																								line-height: 40px;
																								margin: 0px 0;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--vertical
																								.cc-menu--item {
																								line-height: 40px;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								> .cc-menu--nav
																								> .cc-menu--item {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item.block,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item.current,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item:hover {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"] {
																								font-size: 16px;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.cc-menu--item.block,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.cc-menu--item.current,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.cc-menu--item:hover {
																								border-bottom-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item__link {
																								color: #333333;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item.active
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item {
																								background-color: rgba(
																									0,
																									181,
																									174,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover {
																								background-color: rgba(
																									0,
																									152,
																									146,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								color: #666666;
																								text-align: left;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link {
																								color: #0064d8;
																								text-align: left;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: flex-start;
																								text-align: left;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: space-between;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item__link
																								> i {
																								width: 30px;
																								transform: rotate(-90deg);
																								transform-origin: 50% 50%;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--auto
																								> .cc-menu--nav {
																								font-size: 16px;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu.cc-menu--auto__mini
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-66-eizdndxdbx"].cc-menu--auto
																									.cc-menu--expand__header {
																									display: block;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--auto
																									> .cc-menu--nav {
																									display: none;
																									opacity: 0; /*position: fixed;*/
																									position: relative;
																									z-index: 25;
																									width: 100%;
																									left: 0;
																									top: 50px;
																									height: calc(100% - 50px);
																									padding: 0 10px;
																									box-sizing: border-box;
																									overflow: hidden;
																									overflow-y: auto;
																								}
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--nav
																									> .cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--horizontal
																									> .cc-menu--nav
																									> .cc-menu--item {
																									height: 60px;
																									line-height: 60px;
																									padding: 0 20px;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--vertical
																									> .cc-menu--nav
																									> .cc-menu--item {
																									margin: 20px 0;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--vertical
																									.cc-menu--item {
																									line-height: 60px;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item {
																									background-color: #fff;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item.block,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item.current,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item:hover {
																									background-color: #fff;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																									.cc-menu--item.block,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																									.cc-menu--item.current,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																									.cc-menu--item:hover {
																									border-bottom-color: #fff;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item__link {
																									color: #3c3c3c;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item:hover {
																									background-color: #009892;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu.cc-menu--auto__mini
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									height: 43px;
																									line-height: 43px;
																									margin: 0px 0;
																								}
																							}
																							@media only screen and (min-width: 767px) {
																							}
																						</style>
																						<div
																							node-id="id-66-eizdndxdbx"
																							node-type="menu"
																							class="cc-menu cc-menu--style__default cc-menu--vertical cc-menu--arrow-icon cc-menu--line-main">
																							<ul class="cc-menu--nav">
																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="ai%E5%BA%94%E7%94%A8/aigc.html">
																											<span
																												class="cc-menu--item__title">
																												垂類大模型
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="ai%E5%BA%94%E7%94%A8/%E6%95%B0%E6%99%BA%E4%BA%BA.html">
																											<span
																												class="cc-menu--item__title">
																												數智人
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="ai%E5%BA%94%E7%94%A8/%E5%9E%82%E7%B1%BB%E5%A4%A7%E6%A8%A1%E5%9E%8B.html">
																											<span
																												class="cc-menu--item__title">
																												AI 應用
																											</span>
																										</a>
																									</div>
																								</li>
																							</ul>
																						</div>

																						<script>
																							;(function () {
																								useComponent("menu").default({
																									id: "id-66-eizdndxdbx",
																									options: {
																										hover_show: "no",
																										show_cur_sub: "yes",
																										retain_hover: "none",
																										"line-style-obj":
																											"main_menu",
																										"line-style": "left",
																										mode: "vertical",
																										style: "default",
																										"menu-item-repulsion": "no"
																									}
																								})
																							})()
																						</script>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	node-id="id-19-v5tkhhkkj4"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-8 cc-col-lg3-8 cc-col-lg2-8 cc-col-lg-8 cc-col-md-8 cc-col-sm-8 cc-col-xs-24">
																	<style style-id="id-19-v5tkhhkkj4">
																		[node-id="id-19-v5tkhhkkj4"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																		@media only screen and (min-width: 768px) {
																			[node-id="id-19-v5tkhhkkj4"] {
																				padding-top: 50px;
																				padding-bottom: 80px;
																				padding-left: 50px;
																			}
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-19-v5tkhhkkj4",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-47-ndco63iww1--wrapper">
																		<style style-id="id-47-ndco63iww1">
																			[node-id="id-47-ndco63iww1"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-47-ndco63iww1"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-47-ndco63iww1"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-47-ndco63iww1"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					瞭解<span style="color: #0064d8"
																						>環球數科</span
																					>
																				</p>
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					核心產品！
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-47-ndco63iww1",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																	<div
																		class="cc-element--wrapper id-26-c1ds8m08s6--wrapper">
																		<style style-id="id-26-c1ds8m08s6">
																			[node-id="id-26-c1ds8m08s6"] {
																				padding-top: 40px;
																			}
																			[node-id="id-26-c1ds8m08s6"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-26-c1ds8m08s6"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-26-c1ds8m08s6"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-26-c1ds8m08s6"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p>
																					&ldquo;3+X&rdquo;業務佈局：
																					<a
																						href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85.html"
																						>智慧文旅</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a
																						href="https://hqshuke.com/%e6%99%ba%e6%85%a7%e5%9f%8e%e5%b8%82"
																						>城市服務</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a
																						href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81.html"
																						>智慧生態</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a
																						href="https://hqshuke.com/ai%e5%ba%94%e7%94%a8/%e5%9e%82%e7%b1%bb%e5%a4%a7%e6%a8%a1%e5%9e%8b"
																						>更多行業</a
																					>
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-26-c1ds8m08s6",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
																<div
																	node-id="id-25-edi990jjid"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<style style-id="id-25-edi990jjid">
																		[node-id="id-25-edi990jjid"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-25-edi990jjid",
																				options: []
																			})
																		})()
																	</script>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="
																height: 430px;
																background: rgba(250, 250, 250, 1);
															">
															<div
																node-id="id-65-rg1fdfgvaf"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__full">
																<style style-id="id-65-rg1fdfgvaf">
																	[node-id="id-65-rg1fdfgvaf"] {
																		background-color: rgba(229, 239, 251, 1);
																	}
																</style>
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-65-rg1fdfgvaf",
																			options: {
																				"full-width": "full",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-88-mme92kv1ar"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-88-mme92kv1ar",
																				options: []
																			})
																		})()
																	</script>
																</div>
																<div
																	node-id="id-31-ng2rngvclg"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-31-ng2rngvclg",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-51-x5jjg9mffn--wrapper">
																		<div
																			node-id="id-51-x5jjg9mffn"
																			node-type="block"
																			class="cc-block cc-slot--wrapper">
																			<style style-id="id-51-x5jjg9mffn">
																				@media only screen and (min-width: 768px) {
																					[node-id="id-51-x5jjg9mffn"] {
																						padding-top: 30px;
																					}
																				}
																				[node-id="id-51-x5jjg9mffn"] {
																					height: 430px;
																					overflow: hidden;
																					overflow-y: auto;
																				}
																			</style>
																			<script>
																				;(function () {
																					useComponent("block").default({
																						id: "id-51-x5jjg9mffn",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				node-id="id-78-w949t97y4v"
																				node-type="row"
																				class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																				<script>
																					;(function () {
																						useComponent("row").default({
																							id: "id-78-w949t97y4v",
																							options: {
																								"full-width": "default",
																								"adaption-height": "no",
																								"background-video": "",
																								"noheader-full-height": "no",
																								"auto-flex": [],
																								"auto-flex-enable": "no"
																							}
																						})
																					})()
																				</script>
																				<div
																					node-id="id-62-ocbx635o84"
																					node-type="column"
																					class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																					<script>
																						;(function () {
																							useComponent("column").default({
																								id: "id-62-ocbx635o84",
																								options: []
																							})
																						})()
																					</script>
																					<div
																						class="cc-element--wrapper id-64-vsnh2e3otr--wrapper">
																						<style style-id="id-64-vsnh2e3otr">
																							[node-id="id-64-vsnh2e3otr"]
																								.cc-textblock__body {
																								padding: 15px;
																							}
																							[node-id="id-64-vsnh2e3otr"].cc-textblock
																								.cc-textblock__body,
																							[node-id="id-64-vsnh2e3otr"].cc-textblock
																								.cc-textblock__body
																								* {
																								table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																								word-wrap: break-word; /*英文單字因自動換行*/
																								word-break: normal; /*正常避頭尾 */
																								text-align: justify; /*文字左右對齊*/
																								text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																							}
																						</style>
																						<div
																							node-id="id-64-vsnh2e3otr"
																							node-type="textblock"
																							class="cc-textblock">
																							<div
																								class="cc-textblock__body richtext">
																								<p
																									class="font-64361d5afc91249e3bd51e624b693b37">
																									成功案例
																								</p>
																							</div>
																						</div>

																						<script>
																							;(function () {
																								useComponent(
																									"textblock"
																								).default({
																									id: "id-64-vsnh2e3otr",
																									options: []
																								})
																							})()
																						</script>
																					</div>
																					<div
																						class="cc-element--wrapper id-96-qs92ow1w1r--wrapper">
																						<style style-id="id-96-qs92ow1w1r">
																							[node-id="id-96-qs92ow1w1r"].cc-menu.cc-menu--vertical
																								.cc-menu--nav
																								.cc-menu--item {
																								box-sizing: border-box;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.line_box {
																								background: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.cc-menu--item.current
																								> .line_box {
																								width: 100%;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								li.menu {
																								color: #000000;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--nav
																								> .cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--horizontal {
																								text-align: left;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--horizontal
																								> .cc-menu--nav
																								> .cc-menu--item {
																								height: 40px;
																								line-height: 40px;
																								padding: 0 20px;
																								margin: 0px 0px;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--vertical
																								> .cc-menu--nav
																								> .cc-menu--item {
																								margin: 0px 0;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								height: 40px;
																								line-height: 40px;
																								margin: 0px 0;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--vertical
																								.cc-menu--item {
																								line-height: 40px;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								> .cc-menu--nav
																								> .cc-menu--item {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item.block,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item.current,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item:hover {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"] {
																								font-size: 16px;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.cc-menu--item.block,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.cc-menu--item.current,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.cc-menu--item:hover {
																								border-bottom-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item__link {
																								color: #333333;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item.active
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item {
																								background-color: rgba(
																									0,
																									181,
																									174,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover {
																								background-color: rgba(
																									0,
																									152,
																									146,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								color: #666666;
																								text-align: left;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link {
																								color: #0064d8;
																								text-align: left;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: flex-start;
																								text-align: left;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: space-between;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item__link
																								> i {
																								width: 30px;
																								transform: rotate(-90deg);
																								transform-origin: 50% 50%;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--auto
																								> .cc-menu--nav {
																								font-size: 16px;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu.cc-menu--auto__mini
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-96-qs92ow1w1r"].cc-menu--auto
																									.cc-menu--expand__header {
																									display: block;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--auto
																									> .cc-menu--nav {
																									display: none;
																									opacity: 0; /*position: fixed;*/
																									position: relative;
																									z-index: 25;
																									width: 100%;
																									left: 0;
																									top: 50px;
																									height: calc(100% - 50px);
																									padding: 0 10px;
																									box-sizing: border-box;
																									overflow: hidden;
																									overflow-y: auto;
																								}
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--nav
																									> .cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--horizontal
																									> .cc-menu--nav
																									> .cc-menu--item {
																									height: 60px;
																									line-height: 60px;
																									padding: 0 20px;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--vertical
																									> .cc-menu--nav
																									> .cc-menu--item {
																									margin: 20px 0;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--vertical
																									.cc-menu--item {
																									line-height: 60px;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item {
																									background-color: #fff;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item.block,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item.current,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item:hover {
																									background-color: #fff;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																									.cc-menu--item.block,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																									.cc-menu--item.current,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																									.cc-menu--item:hover {
																									border-bottom-color: #fff;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item__link {
																									color: #3c3c3c;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item:hover {
																									background-color: #009892;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu.cc-menu--auto__mini
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									height: 43px;
																									line-height: 43px;
																									margin: 0px 0;
																								}
																							}
																							@media only screen and (min-width: 767px) {
																							}
																						</style>
																						<div
																							node-id="id-96-qs92ow1w1r"
																							node-type="menu"
																							class="cc-menu cc-menu--style__default cc-menu--vertical cc-menu--arrow-icon cc-menu--line-main">
																							<ul class="cc-menu--nav">
																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1-copy-copy-2/%E6%99%BA%E6%85%A7%E7%9B%91%E7%AE%A1-1.html">
																											<span
																												class="cc-menu--item__title">
																												智慧文旅
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1-copy-copy-2/%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1-copy-copy.html">
																											<span
																												class="cc-menu--item__title">
																												城市服務
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1-copy-copy-2/%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1-copy.html">
																											<span
																												class="cc-menu--item__title">
																												智慧生態
																											</span>
																										</a>
																									</div>
																								</li>
																							</ul>
																						</div>

																						<script>
																							;(function () {
																								useComponent("menu").default({
																									id: "id-96-qs92ow1w1r",
																									options: {
																										hover_show: "no",
																										show_cur_sub: "yes",
																										retain_hover: "none",
																										"line-style-obj":
																											"main_menu",
																										"line-style": "left",
																										mode: "vertical",
																										style: "default",
																										"menu-item-repulsion": "no"
																									}
																								})
																							})()
																						</script>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	node-id="id-54-rezgbka2gi"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-54-rezgbka2gi",
																				options: []
																			})
																		})()
																	</script>
																</div>
																<div
																	node-id="id-46-a3andl1s9d"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-8 cc-col-lg3-8 cc-col-lg2-8 cc-col-lg-8 cc-col-md-8 cc-col-sm-8 cc-col-xs-24">
																	<style style-id="id-46-a3andl1s9d">
																		[node-id="id-46-a3andl1s9d"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																		@media only screen and (min-width: 768px) {
																			[node-id="id-46-a3andl1s9d"] {
																				padding-top: 50px;
																				padding-bottom: 80px;
																				padding-left: 50px;
																			}
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-46-a3andl1s9d",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-33-xti5y10y1y--wrapper">
																		<style style-id="id-33-xti5y10y1y">
																			[node-id="id-33-xti5y10y1y"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-33-xti5y10y1y"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-33-xti5y10y1y"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-33-xti5y10y1y"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					瞭解<span style="color: #0064d8"
																						>環球數科</span
																					>
																				</p>
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					核心產品！
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-33-xti5y10y1y",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																	<div
																		class="cc-element--wrapper id-29-hk77z33i6e--wrapper">
																		<style style-id="id-29-hk77z33i6e">
																			[node-id="id-29-hk77z33i6e"] {
																				padding-top: 40px;
																			}
																			[node-id="id-29-hk77z33i6e"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-29-hk77z33i6e"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-29-hk77z33i6e"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-29-hk77z33i6e"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p>
																					“3+X”業務佈局：
																					<a
																						href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85.html"
																						>智慧文旅</a
																					>    |   
																					<a
																						href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82-1.html"
																						>智慧城市</a
																					>    |   
																					<a
																						href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81.html"
																						>智慧生態</a
																					>    |   
																					<a href="/通用解決方案">更多行業</a>
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-29-hk77z33i6e",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
																<div
																	node-id="id-85-c0j06hhhae"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<style style-id="id-85-c0j06hhhae">
																		[node-id="id-85-c0j06hhhae"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-85-c0j06hhhae",
																				options: []
																			})
																		})()
																	</script>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="
																height: 430px;
																background: rgba(250, 250, 250, 1);
															">
															<div
																node-id="id-15-96ie10ai9h"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-15-96ie10ai9h",
																			options: {
																				"full-width": "default",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-14-248l29jedn8"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-14-248l29jedn8",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-13-30e961u44h8--wrapper">
																		<div
																			node-id="id-13-30e961u44h8"
																			node-type="placeholder"
																			class="cc-placeholder"
																			style="height: 50px"></div>

																		<script>
																			;(function () {
																				useComponent("placeholder").default({
																					id: "id-13-30e961u44h8",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="
																height: 430px;
																background: rgba(250, 250, 250, 1);
															">
															<div
																node-id="id-18-urn9q83d06g"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-18-urn9q83d06g",
																			options: {
																				"full-width": "default",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-17-akos8nnpsp"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-17-akos8nnpsp",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-16-6fkulllotpo--wrapper">
																		<div
																			node-id="id-16-6fkulllotpo"
																			node-type="placeholder"
																			class="cc-placeholder"
																			style="height: 50px"></div>

																		<script>
																			;(function () {
																				useComponent("placeholder").default({
																					id: "id-16-6fkulllotpo",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="height: 300px; background: #fff">
															<div
																node-id="id-21-gbmf0a7qui"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-21-gbmf0a7qui",
																			options: {
																				"full-width": "default",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-20-rlok5g7u0l"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-20-rlok5g7u0l",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-19-9fg87k3v2ro--wrapper">
																		<div
																			node-id="id-19-9fg87k3v2ro"
																			node-type="placeholder"
																			class="cc-placeholder"
																			style="height: 50px"></div>

																		<script>
																			;(function () {
																				useComponent("placeholder").default({
																					id: "id-19-9fg87k3v2ro",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="Page-header--widgets"></div>
							</div>
						</div>
					</div>
					<div class="Page-header--main__placeholder"></div>
				</div>
				<div class="Page-body">
					<div class="Page-sidebar sidebar-left"></div>
					<div class="Page-content">
						<div
							node-id="id-10-9bs857pt6vo"
							node-type="row"
							class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__row">
							<style style-id="id-10-9bs857pt6vo">
								[node-id="id-10-9bs857pt6vo"] {
									/*background-image: url(uploads/sites/1012/2022/12/6e09670b5b1967b8fdf1a24e648c73erer.jpg);*/
									background-image: url(uploads/sites/1012/2022/12/关于我们新.jpg);
									background-size: cover;
									background-position: center center;
									background-repeat: repeat;
								}
							</style>
							<script>
								;(function () {
									useComponent("row").default({
										id: "id-10-9bs857pt6vo",
										options: {
											"full-width": "row",
											"adaption-height": "no",
											"background-video": "",
											"noheader-full-height": "no",
											"auto-flex": [],
											"auto-flex-enable": "no"
										}
									})
								})()
							</script>
							<div
								node-id="id-11-r7htj5ul6m"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-11-r7htj5ul6m",
											options: []
										})
									})()
								</script>
								<div class="cc-element--wrapper id-12-ln308vdq5m8--wrapper">
									<div
										node-id="id-12-ln308vdq5m8"
										node-type="block"
										class="cc-block cc-slot--wrapper">
										<style style-id="id-12-ln308vdq5m8">
											@media only screen and (max-width: 767px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 50px;
													padding-bottom: 50px;
												}
											}
											@media only screen and (min-width: 768px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 120px;
													padding-bottom: 120px;
												}
											}
											@media only screen and (min-width: 1200px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 150px;
													padding-bottom: 150px;
												}
											}
											@media only screen and (min-width: 1360px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 180px;
													padding-bottom: 180px;
												}
											}
											@media only screen and (min-width: 1600px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 220px;
													padding-bottom: 220px;
												}
											}
											@media only screen and (min-width: 1920px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 220px;
													padding-bottom: 220px;
												}
											}
										</style>
										<script>
											;(function () {
												useComponent("block").default({
													id: "id-12-ln308vdq5m8",
													options: []
												})
											})()
										</script>
										<div
											node-id="id-13-k1kpva4ou3o"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-13-k1kpva4ou3o",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-14-cppslltolvo"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-14-cppslltolvo",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-15-t5efknkdj1g--wrapper">
													<style style-id="id-15-t5efknkdj1g">
														[node-id="id-15-t5efknkdj1g"] .cc-textblock__body {
															padding: 20px;
														}
														[node-id="id-15-t5efknkdj1g"].cc-textblock
															.cc-textblock__body,
														[node-id="id-15-t5efknkdj1g"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-15-t5efknkdj1g"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p
																class="font-1ea24a2ccc91fca49f6e4455d2a5f757"
																style="line-height: 1">
																<strong
																	><span style="color: #ffffff"
																		>Contact Us</span
																	></strong
																>
															</p>
<!--															<p-->
<!--																class="font-64361d5afc91249e3bd51e624b693b37"-->
<!--																style="line-height: 1">-->
<!--																<span style="color: #ffffff"-->
<!--																	><span>CONTACT US</span></span-->
<!--																>-->
<!--															</p>-->
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-15-t5efknkdj1g",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div
							node-id="id-10-4snv5h6ng38"
							node-type="row"
							class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__row">
							<style style-id="id-10-4snv5h6ng38">
								[node-id="id-10-4snv5h6ng38"] {
									background-position: center top;
									background-repeat: repeat-x;
									background-size: cover;
								}
							</style>
							<script>
								;(function () {
									useComponent("row").default({
										id: "id-10-4snv5h6ng38",
										options: {
											"full-width": "row",
											"adaption-height": "no",
											"background-video": "",
											"noheader-full-height": "no",
											"auto-flex": [],
											"auto-flex-enable": "no"
										}
									})
								})()
							</script>
							<div
								style="
									display: flex;
									height: 500px;
									margin: 80px 0;
									width: 100%;
								">
								<div
									style="
										background: url('./adress.png');
										background-size: cover;
										background-position: center;
										height: 100%;
										flex: 1;
									"></div>
								<div
									style="background: #f8f8f8; width: 350px; padding: 80px 50px">
									<h2 style="margin-bottom: 40px">Digital Origin (Hong Kong) Limited</h2>
									<p style="line-height: 30px; margin-bottom: 20px">
										Address: Room 608-613, 6/F, Block C, Cyberport 3, 100 Cyberport Road, Hong Kong
									</p>
									<p style="line-height: 30px; margin-bottom: 20px">
										Tel: 00852-56147493
									</p>
									<p style="line-height: 30px; margin-bottom: 20px">
										Email: <EMAIL>; <EMAIL>
									</p>
								</div>
							</div>
						</div>
					</div>
					<div class="Page-sidebar sidebar-right"></div>
				</div>

				<div
					class="Page-footer"
					style="background: #020e26; display: flex; justify-content: center">
					<div class="cc-textblock__body richtext">
						<div style="color: white; font-size: 20px">Contact Details</div>
						<p style="line-height: 1.6">
							<span style="color: #7b8595; font-size: 14px"
								>Company: Digital Origin (Hong Kong) Limited</span
							>
						</p>
						<p style="line-height: 1.6">
							<span style="color: #7b8595; font-size: 14px"
								>Address: Room 608-613, 6/F, Block C, Cyberport 3, 100 Cyberport Road, Hong Kong</span
							>
						</p>
						<p style="line-height: 1.6">
							<span style="color: #7b8595; font-size: 14px"
								>Tel: 00852-56147493</span
							>
						</p>
						<!-- <p style="line-height: 1.6;"><span style="color: #7b8595; font-size: 14px;">投訴：0755-88328980</span></p> -->
						<p style="line-height: 1.6; text-align: left">
							<span style="color: #7b8595; font-size: 14px"
								>Email: <EMAIL>; <EMAIL></span
							>
						</p>
					</div>
					<div class="cc-textblock__body richtext">
						<div style="color: white; font-size: 20px">Follow us</div>
						<div
							node-id="id-40-k3cqy7zenf"
							node-type="imagelist"
							class="cc-imagelist cc-imagelist--grid">
							<div class="cc-imagelist-wrapper">
								<div class="cc-imagelist--items">
									<div class="cc-imagelist--item">
										<div class="cc-imagelist--item__link">
											<a href="" title="" target="_self">
												<div class="cc-imagelist--item__image">
													<img
														src="uploads/sites/1012/2022/11/14df9f2ffdcd56437226404d4d0993a1.jpg" />
												</div>
												<div class="cc-imagelist--item__title"></div>
												<div class="cc-imagelist--item__mobile-title"></div>
											</a>
										</div>
									</div>
									<div class="cc-imagelist--item">
										<div class="cc-imagelist--item__link">
											<a href="" title="" target="_self">
												<div class="cc-imagelist--item__image">
													<img
														src="uploads/sites/1012/2022/11/d9817bde75e239270021cd0e76b9e947.jpg" />
												</div>
												<div class="cc-imagelist--item__title"></div>
												<div class="cc-imagelist--item__mobile-title"></div>
											</a>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="Page-widgets">
					<div class="side-toolbar position-right-bottom">
						<ul>
							<li>
								<!-- <div class="toolbar-icon
                ">
                <a target="_self" title="在線客服" onclick="javascript:_utils_.handler(JSON.parse(decodeURIComponent('%7B%22action%22%3A%22open%22%2C%22options%22%3A%7B%22qq%22%3A%22919618856%22%2C%22target%22%3A%22_self%22%2C%22url%22%3A%22https%3A%5C%2F%5C%2Fgcluster.shukeyun.com%5C%2Fmeta%5C%2Fchat%5C%2F%22%7D%7D')));" href="javascript:;"><img src="uploads/sites/1012/2024/07/482b135a21e71b06ae1227aaf7083817.png"></a>            </div> -->
							</li>
							<li>
								<div class="toolbar-icon">
									<a
										target="_self"
										title="返回頂部"
										onclick="javascript:_utils_.handler(JSON.parse(decodeURIComponent('%7B%22action%22%3A%22backtop%22%2C%22options%22%3A%5B%5D%7D')));"
										href="javascript:;"
										><img
											src="uploads/sites/1012/2024/09/11e5641042b1b3cfe9a1a3ec4073aff5.png"
									/></a>
								</div>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<script class="custom-js-code">
			;(function () {
				var custom_js_code = [{ title: "js\u793a\u4f8b", code: "" }]
				for (var i = 0; i < custom_js_code.length; i++) {
					var code = custom_js_code[i].code
					var title = custom_js_code[i].title
					_utils_.sandbox(code, title)
				}
			})()
		</script>
	</body>
</html>
