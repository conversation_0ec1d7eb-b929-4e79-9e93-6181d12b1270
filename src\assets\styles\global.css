/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
}

/* 重置默认样式 */
ul, ol {
  list-style: none;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

img {
  max-width: 100%;
  height: auto;
}

/* 自定义阴影类 */
.custom-yy {
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
}

.custom-al {
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.d-flex {
  display: flex;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* 颜色变量 */
:root {
  --primary-color: #0064d8;
  --primary-hover: #0081f9;
  --secondary-color: #1064a9;
  --text-color: #333333;
  --text-muted: #666666;
  --text-light: #7b8595;
  --border-color: #eeeeee;
  --background-light: #f8f9fa;
  --background-blue: rgba(0, 100, 216, 0.31);
  --white: #ffffff;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 按钮基础样式 */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-white {
  background-color: var(--white);
  color: var(--text-color);
  border-color: var(--border-color);
}

.btn-white:hover {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

/* 卡片样式 */
.card {
  background: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-body {
  padding: 20px;
}

/* 响应式网格 */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col {
  flex: 1;
  padding: 0 15px;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* Hover 效果 */
.hvr-sweep-to-top {
  position: relative;
  overflow: hidden;
}

.hvr-sweep-to-top:before {
  content: '';
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-color);
  transform: scaleY(0);
  transform-origin: 50% 100%;
  transition: transform 0.3s ease-out;
}

.hvr-sweep-to-top:hover:before {
  transform: scaleY(1);
}

.hvr-float {
  transition: transform 0.3s ease-out;
}

.hvr-float:hover {
  transform: translateY(-5px);
}

/* 响应式断点 */
@media (max-width: 1200px) {
  .container {
    max-width: 960px;
  }
}

@media (max-width: 992px) {
  .container {
    max-width: 720px;
  }
  
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-md-3 { flex: 0 0 25%; max-width: 25%; }
}

@media (max-width: 768px) {
  .container {
    max-width: 540px;
  }
  
  .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
  .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
  .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
}

@media (max-width: 576px) {
  .container {
    max-width: 100%;
    padding: 0 15px;
  }
  
  .col-xs-12 { flex: 0 0 100%; max-width: 100%; }
}
