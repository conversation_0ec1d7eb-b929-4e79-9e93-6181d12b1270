<script setup>
// App.vue - 应用根组件
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
</script>

<template>
  <div id="app">
    <!-- 页面头部组件 -->
    <AppHeader />
    <!-- Vue Router 视图容器 -->
    <router-view />
    <!-- 页面底部组件 -->
    <AppFooter />
  </div>
</template>

<style>
/* 全局应用样式 */
#app {
  min-height: 100vh;
  width: 100%;
}

/* 确保路由视图占满容器 */
.router-view {
  width: 100%;
  min-height: 100vh;
}
</style>
