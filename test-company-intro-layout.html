<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公司简介左右分栏布局测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 公司简介模块 */
        .company-intro {
            padding: 60px 0;
            background: url('/uploads/sites/1012/2022/11/8c42f6797950f4aa3e1d8eb632b7ddb5.jpg') center/cover;
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            position: relative;
            z-index: 1;
        }

        .intro-layout {
            display: flex;
            gap: 50px;
            align-items: flex-start;
            position: relative;
            z-index: 2;
        }

        /* 左侧公司简介 */
        .intro-left {
            flex: 0 0 37.5%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .company-logo {
            margin-bottom: 30px;
        }

        .company-logo img {
            max-width: 200px;
            height: auto;
        }

        .intro-content {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: left;
        }

        .intro-subtitle {
            color: #0064d8;
            font-size: 16px;
            font-weight: 500;
            margin: 0 0 15px 0;
            line-height: 1.5;
            text-align: justify;
        }

        .intro-title {
            color: #333;
            font-size: 24px;
            font-weight: bold;
            margin: 0 0 15px 0;
            line-height: 1.5;
            text-align: justify;
        }

        .intro-divider {
            margin: 15px 0;
            text-align: left;
        }

        .intro-text {
            color: #333;
            font-size: 16px;
            line-height: 2;
            margin: 0;
            text-align: justify;
            text-justify: inter-ideograph;
        }

        .view-more-btn {
            background: rgba(0, 129, 249, 1);
            color: #ffffff;
            border: 1px solid #ccc;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            margin-top: 20px;
        }

        .view-more-btn:hover {
            background: rgba(0, 100, 216, 1);
        }

        /* 右侧统计数据 */
        .intro-right {
            flex: 1;
            padding-left: 30px;
        }

        .statistics-grid {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .stat-card {
            background: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
            display: flex;
            align-items: baseline;
            justify-content: center;
            margin-bottom: 15px;
        }

        .counter {
            font-size: 60px;
            font-weight: bold;
            color: #0064d8;
            line-height: 1;
        }

        .plus {
            font-size: 60px;
            font-weight: bold;
            color: #0064d8;
            margin-left: 5px;
        }

        .stat-card h3 {
            color: #333;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 15px 0;
        }

        .stat-card p {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin: 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .company-intro {
                padding: 40px 0;
            }

            .intro-layout {
                flex-direction: column;
                gap: 30px;
            }

            .intro-left {
                flex: none;
                width: 100%;
            }

            .intro-right {
                padding-left: 0;
            }

            .statistics-grid {
                gap: 20px;
            }

            .intro-title {
                font-size: 20px;
            }

            .intro-text {
                font-size: 14px;
                line-height: 1.8;
            }

            .stat-card {
                padding: 20px;
            }

            .counter, .plus {
                font-size: 48px;
            }

            .stat-card h3 {
                font-size: 16px;
            }

            .stat-card p {
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <!-- 公司简介模块 -->
    <section class="company-intro" id="集团简介">
        <div class="container">
            <div class="intro-layout">
                <!-- 左侧公司简介 -->
                <div class="intro-left">
                    <div class="company-logo">
                        <img src="/uploads/sites/1012/2022/11/18b3630d73e2d35aa818fdedb18cd8e0.png" alt="公司标识" />
                    </div>
                    <div class="intro-content">
                        <p class="intro-subtitle">WHO WE ARE</p>
                        <h2 class="intro-title">數字起源 (香港) 有限公司</h2>
                        <div class="intro-divider">
                            <img src="/uploads/sites/1012/2022/11/15d5fcbdf0b8f197d7a8bb0f076ad841.jpg" alt="分割线" width="101" height="6" />
                        </div>
                        <div class="intro-description">
                            <p class="intro-text">
                                系環球數科股份有限公司（以下簡稱"集團"）下屬機構，作為集團海外總部、創新研發中心、全球運營中心，
                                公司將基於集團 20 餘年的行業沉澱及技術積累，依托香港在區位、政策、人才等方面的優勢，
                                聚焦前沿技術研究、技術驗證、產品預研及海外市場拓展，形成國內與海外市場雙循環的出海新格局。
                                數字起源，連接無限可能，賦能美好生活。
                            </p>
                        </div>
                        <div class="view-more-section">
                            <button class="view-more-btn">
                                查看更多 >>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 右侧统计数据 -->
                <div class="intro-right">
                    <div class="statistics-grid">
                        <div class="stat-card">
                            <div class="stat-number">
                                <span class="counter">24</span>
                                <span class="plus">+</span>
                            </div>
                            <h3>行業理解</h3>
                            <p>創新構建"AI 大腦+PaaS 引擎+SaaS 產品"的"雲智鏈一體"行業解決方案及全棧技術能力。</p>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">
                                <span class="counter">6</span>
                                <span class="plus">+</span>
                            </div>
                            <h3>6 大技術支持落地</h3>
                            <p>廣泛佈局前沿領域，加速推動 AI 在各垂直場景中的應用落地，為行業賦能。</p>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">
                                <span class="counter">3</span>
                                <span class="plus">+X</span>
                            </div>
                            <h3>場景落地</h3>
                            <p>以先發行業智慧文旅為起點，有序擴展智慧生態和城市服務領域。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
