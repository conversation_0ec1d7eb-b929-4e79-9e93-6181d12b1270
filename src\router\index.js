import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('../views/Home/index.vue')
    },
    {
      path: '/slider-test',
      name: 'SliderTest',
      component: () => import('../views/SliderTest.vue')
    }
  ],
})

export default router
