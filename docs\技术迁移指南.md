# Vue 3 技术迁移指南

## 1. 项目初始化

### 1.1 创建Vue 3项目
```bash
# 使用Vite创建Vue 3 + TypeScript项目
npm create vue@latest vue3-website

# 选择配置
✔ Add TypeScript? … Yes
✔ Add JSX Support? … No
✔ Add Vue Router for Single Page Application development? … Yes
✔ Add Pinia for state management? … Yes
✔ Add Vitest for Unit Testing? … Yes
✔ Add an End-to-End Testing Solution? › Playwright
✔ Add ESLint for code quality? … Yes
✔ Add Prettier for code formatting? … Yes

cd vue3-website
npm install
```

### 1.2 安装额外依赖
```bash
# UI组件库（可选）
npm install element-plus

# 国际化
npm install vue-i18n@9

# 样式预处理器
npm install -D sass

# 工具库
npm install lodash-es
npm install -D @types/lodash-es

# 图标库
npm install @fortawesome/fontawesome-free
```

### 1.3 项目结构调整
```
src/
├── assets/
│   ├── images/          # 从oldCode/uploads/迁移图片
│   ├── styles/          # 样式文件
│   │   ├── variables.scss
│   │   ├── mixins.scss
│   │   ├── base.scss
│   │   └── components.scss
│   └── fonts/           # 字体文件
├── components/
│   ├── common/          # 通用组件
│   │   ├── AppButton.vue
│   │   ├── AppRow.vue
│   │   ├── AppCol.vue
│   │   └── AppPicture.vue
│   ├── layout/          # 布局组件
│   │   ├── AppLayout.vue
│   │   ├── AppHeader.vue
│   │   ├── AppFooter.vue
│   │   └── AppNavigation.vue
│   └── business/        # 业务组件
├── views/               # 页面组件
├── router/
├── stores/
├── composables/
├── utils/
├── types/
└── locales/
```

## 2. 样式系统迁移

### 2.1 CSS变量迁移
将原有的CSS变量系统迁移到SCSS中：

```scss
// src/assets/styles/variables.scss
:root {
  // 颜色系统
  --theme-color: #3095fb;
  --theme-color-dark: #1989fa;
  --theme-color-light: #a3d0fd;
  --text-color: #3c3c3c;
  --border-color: #ebedf0;
  --background-color: #f8f8f8;

  // 尺寸系统
  --center-width: 1200px;
  --font-size: 16px;

  // 间距系统
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

// SCSS变量（用于计算和函数）
$theme-color: #3095fb;
$text-color: #3c3c3c;
$border-color: #ebedf0;
$center-width: 1200px;

// 断点系统
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);
```

### 2.2 工具类迁移
```scss
// src/assets/styles/utilities.scss

// 布局工具类
.lay-fx { display: flex; }
.lay-fd-r { flex-direction: row; }
.lay-fd-c { flex-direction: column; }
.lay-jc-c { justify-content: center; }
.lay-jc-sb { justify-content: space-between; }
.lay-ai-c { align-items: center; }

// 间距工具类
@for $i from 1 through 10 {
  .m-t-#{$i * 5} { margin-top: #{$i * 5}px; }
  .m-b-#{$i * 5} { margin-bottom: #{$i * 5}px; }
  .m-l-#{$i * 5} { margin-left: #{$i * 5}px; }
  .m-r-#{$i * 5} { margin-right: #{$i * 5}px; }

  .p-t-#{$i * 5} { padding-top: #{$i * 5}px; }
  .p-b-#{$i * 5} { padding-bottom: #{$i * 5}px; }
  .p-l-#{$i * 5} { padding-left: #{$i * 5}px; }
  .p-r-#{$i * 5} { padding-right: #{$i * 5}px; }
}

// 文本工具类
.lay-tc { text-align: center; }
.lay-tl { text-align: left; }
.lay-tr { text-align: right; }

// 字体大小
.f-s-12 { font-size: 12px; }
.f-s-14 { font-size: 14px; }
.f-s-16 { font-size: 16px; }
.f-s-22 { font-size: 22px; }
```

### 2.3 响应式Mixins
```scss
// src/assets/styles/mixins.scss

// 响应式断点Mixin
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 使用示例
.example {
  font-size: 14px;

  @include respond-to(md) {
    font-size: 16px;
  }

  @include respond-to(lg) {
    font-size: 18px;
  }
}

// 栅格系统Mixin
@mixin make-col($size, $columns: 24) {
  flex: 0 0 percentage($size / $columns);
  max-width: percentage($size / $columns);
}

// 阴影Mixin
@mixin custom-shadow($type: 'default') {
  @if $type == 'default' {
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
  } @else if $type == 'light' {
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
  }
}
```

## 3. 组件迁移策略

### 3.1 布局组件迁移

#### 3.1.1 AppLayout.vue
```vue
<template>
  <div class="app-layout" :class="layoutClass">
    <AppHeader v-if="showHeader" />
    <main class="app-main">
      <div class="container" v-if="containerized">
        <slot />
      </div>
      <slot v-else />
    </main>
    <AppFooter v-if="showFooter" />
  </div>
</template>

<script setup lang="ts">
interface LayoutProps {
  showHeader?: boolean;
  showFooter?: boolean;
  containerized?: boolean;
  fullWidth?: boolean;
}

const props = withDefaults(defineProps<LayoutProps>(), {
  showHeader: true,
  showFooter: true,
  containerized: true,
  fullWidth: false
});

const layoutClass = computed(() => ({
  'layout-full-width': props.fullWidth,
  'layout-containerized': props.containerized
}));
</script>

<style lang="scss" scoped>
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-main {
  flex: 1;

  .container {
    max-width: var(--center-width);
    margin: 0 auto;
    padding: 0 20px;
  }
}

.layout-full-width {
  .container {
    max-width: none;
    padding: 0;
  }
}
</style>
```

#### 3.1.2 AppHeader.vue
```vue
<template>
  <header class="app-header" :class="headerClass">
    <!-- 顶部栏 -->
    <div v-if="showTopBar" class="header-topbar">
      <div class="container">
        <div class="topbar-content">
          <div class="welcome-message">
            <i class="fas fa-home"></i>
            {{ $t('welcome.message') }}
          </div>
          <div class="topbar-actions">
            <LanguageSwitch />
            <SearchBox />
            <LoginButton />
          </div>
        </div>
      </div>
    </div>

    <!-- 主导航 -->
    <div class="header-main">
      <div class="container">
        <div class="header-content">
          <AppLogo />
          <AppNavigation :items="menuItems" />
          <div class="header-actions">
            <SearchBox v-if="!showTopBar" />
            <UserActions />
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
interface HeaderProps {
  showTopBar?: boolean;
  fixed?: boolean;
  transparent?: boolean;
}

const props = withDefaults(defineProps<HeaderProps>(), {
  showTopBar: true,
  fixed: false,
  transparent: false
});

const headerClass = computed(() => ({
  'header-fixed': props.fixed,
  'header-transparent': props.transparent
}));

// 菜单数据
const menuItems = [
  { id: 'home', label: '首頁', path: '/' },
  {
    id: 'solutions',
    label: '解決方案',
    children: [
      { id: 'smart-tourism', label: '智慧文旅', path: '/solutions/tourism' },
      { id: 'smart-city', label: '智慧城市', path: '/solutions/city' },
      { id: 'smart-ecology', label: '智慧生態', path: '/solutions/ecology' }
    ]
  },
  // ... 更多菜单项
];
</script>
```

### 3.2 功能组件迁移

#### 3.2.1 搜索组件
```vue
<template>
  <div class="search-box" :class="{ 'search-expanded': expanded }">
    <div class="search-input-wrapper">
      <input
        v-model="searchQuery"
        type="text"
        :placeholder="$t('search.placeholder')"
        class="search-input"
        @focus="handleFocus"
        @blur="handleBlur"
        @keyup.enter="handleSearch"
      />
      <button class="search-button" @click="handleSearch">
        <i class="fas fa-search"></i>
      </button>
    </div>

    <!-- 搜索建议 -->
    <div v-if="showSuggestions" class="search-suggestions">
      <div
        v-for="suggestion in suggestions"
        :key="suggestion.id"
        class="suggestion-item"
        @click="selectSuggestion(suggestion)"
      >
        {{ suggestion.title }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface SearchProps {
  placeholder?: string;
  showSuggestions?: boolean;
}

const props = withDefaults(defineProps<SearchProps>(), {
  showSuggestions: true
});

const emit = defineEmits<{
  search: [query: string];
  select: [item: any];
}>();

const searchQuery = ref('');
const expanded = ref(false);
const suggestions = ref([]);
const showSuggestions = computed(() =>
  props.showSuggestions && expanded.value && suggestions.value.length > 0
);

const handleFocus = () => {
  expanded.value = true;
  // 获取搜索建议
  fetchSuggestions();
};

const handleBlur = () => {
  // 延迟收起，允许点击建议项
  setTimeout(() => {
    expanded.value = false;
  }, 200);
};

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    emit('search', searchQuery.value);
  }
};

const selectSuggestion = (suggestion: any) => {
  searchQuery.value = suggestion.title;
  emit('select', suggestion);
  expanded.value = false;
};

const fetchSuggestions = async () => {
  // 实现搜索建议逻辑
  // suggestions.value = await searchAPI.getSuggestions(searchQuery.value);
};
</script>
```

## 4. 路由系统迁移

### 4.1 路由配置
```typescript
// src/router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home/index.vue'),
    meta: {
      title: '首頁',
      keepAlive: true
    }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About/index.vue'),
    meta: {
      title: '關於我們'
    }
  },
  {
    path: '/solutions',
    name: 'Solutions',
    component: () => import('@/views/Solutions/index.vue'),
    meta: {
      title: '解決方案'
    },
    children: [
      {
        path: 'tourism',
        name: 'SmartTourism',
        component: () => import('@/views/Solutions/Tourism.vue'),
        meta: {
          title: '智慧文旅'
        }
      },
      {
        path: 'city',
        name: 'SmartCity',
        component: () => import('@/views/Solutions/City.vue'),
        meta: {
          title: '智慧城市'
        }
      },
      {
        path: 'ecology',
        name: 'SmartEcology',
        component: () => import('@/views/Solutions/Ecology.vue'),
        meta: {
          title: '智慧生態'
        }
      }
    ]
  },
  {
    path: '/insights',
    name: 'Insights',
    component: () => import('@/views/Insights/index.vue'),
    meta: {
      title: '行業洞察'
    }
  },
  {
    path: '/contact',
    name: 'Contact',
    component: () => import('@/views/Contact/index.vue'),
    meta: {
      title: '聯繫我們'
    }
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/Error/404.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 數字起源(香港)有限公司`
  }

  next()
})

export default router
```

### 4.2 页面组件结构
```vue
<!-- src/views/Home/index.vue -->
<template>
  <AppLayout>
    <HeroBanner />
    <CompanyIntro />
    <ProductShowcase />
    <NewsSection />
    <ContactSection />
  </AppLayout>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useHead } from '@vueuse/head'

// SEO优化
useHead({
  title: '首頁 - 數字起源(香港)有限公司',
  meta: [
    {
      name: 'description',
      content: '數字起源(香港)有限公司是一家專注於智慧城市、智慧文旅、智慧生態解決方案的科技公司'
    },
    {
      name: 'keywords',
      content: '智慧城市,智慧文旅,智慧生態,數字化轉型,香港科技公司'
    }
  ]
})

onMounted(() => {
  // 页面初始化逻辑
})
</script>
```

## 5. 状态管理迁移

### 5.1 Pinia Store设计
```typescript
// src/stores/app.ts
import { defineStore } from 'pinia'

interface AppState {
  loading: boolean
  language: 'zh' | 'en'
  theme: 'light' | 'dark'
  sidebarCollapsed: boolean
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    loading: false,
    language: 'zh',
    theme: 'light',
    sidebarCollapsed: false
  }),

  getters: {
    isLoading: (state) => state.loading,
    currentLanguage: (state) => state.language,
    isDarkTheme: (state) => state.theme === 'dark'
  },

  actions: {
    setLoading(loading: boolean) {
      this.loading = loading
    },

    setLanguage(language: 'zh' | 'en') {
      this.language = language
      localStorage.setItem('language', language)
    },

    toggleTheme() {
      this.theme = this.theme === 'light' ? 'dark' : 'light'
      localStorage.setItem('theme', this.theme)
    },

    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    // 初始化应用状态
    initializeApp() {
      // 从localStorage恢复状态
      const savedLanguage = localStorage.getItem('language') as 'zh' | 'en'
      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark'

      if (savedLanguage) {
        this.language = savedLanguage
      }

      if (savedTheme) {
        this.theme = savedTheme
      }
    }
  }
})
```

```typescript
// src/stores/user.ts
import { defineStore } from 'pinia'

interface User {
  id: string
  name: string
  email: string
  avatar?: string
}

interface UserState {
  user: User | null
  isLoggedIn: boolean
  permissions: string[]
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    user: null,
    isLoggedIn: false,
    permissions: []
  }),

  getters: {
    userName: (state) => state.user?.name || '',
    userAvatar: (state) => state.user?.avatar || '',
    hasPermission: (state) => (permission: string) =>
      state.permissions.includes(permission)
  },

  actions: {
    async login(credentials: { email: string; password: string }) {
      try {
        // 调用登录API
        // const response = await authAPI.login(credentials)
        // this.user = response.user
        // this.permissions = response.permissions
        this.isLoggedIn = true
      } catch (error) {
        throw error
      }
    },

    logout() {
      this.user = null
      this.isLoggedIn = false
      this.permissions = []
      localStorage.removeItem('token')
    },

    async fetchUserInfo() {
      try {
        // const userInfo = await userAPI.getCurrentUser()
        // this.user = userInfo
      } catch (error) {
        console.error('Failed to fetch user info:', error)
      }
    }
  }
})
```

## 6. 国际化配置

### 6.1 Vue I18n设置
```typescript
// src/locales/index.ts
import { createI18n } from 'vue-i18n'
import zh from './zh.json'
import en from './en.json'

const messages = {
  zh,
  en
}

const i18n = createI18n({
  legacy: false,
  locale: 'zh',
  fallbackLocale: 'en',
  messages
})

export default i18n
```

```json
// src/locales/zh.json
{
  "welcome": {
    "message": "歡迎來到數字起源 (香港) 有限公司！"
  },
  "nav": {
    "home": "首頁",
    "solutions": "解決方案",
    "insights": "行業洞察",
    "experience": "體驗中心",
    "contact": "聯繫我們",
    "careers": "招賢納士"
  },
  "solutions": {
    "tourism": "智慧文旅",
    "city": "智慧城市",
    "ecology": "智慧生態"
  },
  "search": {
    "placeholder": "搜索..."
  },
  "common": {
    "loading": "加載中...",
    "error": "出錯了",
    "retry": "重試",
    "confirm": "確認",
    "cancel": "取消"
  }
}
```

```json
// src/locales/en.json
{
  "welcome": {
    "message": "Welcome to DataOrigin (Hong Kong) Limited!"
  },
  "nav": {
    "home": "Home",
    "solutions": "Solutions",
    "insights": "Insights",
    "experience": "Experience Center",
    "contact": "Contact Us",
    "careers": "Careers"
  },
  "solutions": {
    "tourism": "Smart Tourism",
    "city": "Smart City",
    "ecology": "Smart Ecology"
  },
  "search": {
    "placeholder": "Search..."
  },
  "common": {
    "loading": "Loading...",
    "error": "Error occurred",
    "retry": "Retry",
    "confirm": "Confirm",
    "cancel": "Cancel"
  }
}
```