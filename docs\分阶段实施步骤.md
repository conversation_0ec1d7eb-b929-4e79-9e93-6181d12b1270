# Vue 3重构实施步骤详细指南

## 阶段一：项目初始化和环境搭建（预计1-2天）

### 第1步：创建Vue 3项目
```bash
# 1. 创建项目
npm create vue@latest dataorigin-website-vue3

# 2. 进入项目目录
cd dataorigin-website-vue3

# 3. 安装依赖
npm install

# 4. 安装额外依赖
npm install vue-i18n@9 @fortawesome/fontawesome-free lodash-es
npm install -D sass @types/lodash-es

# 5. 启动开发服务器测试
npm run dev
```

### 第2步：项目结构调整
```bash
# 创建必要的目录结构
mkdir -p src/components/common
mkdir -p src/components/layout
mkdir -p src/components/business
mkdir -p src/views/Home
mkdir -p src/views/About
mkdir -p src/views/Solutions
mkdir -p src/views/Insights
mkdir -p src/views/Contact
mkdir -p src/assets/images
mkdir -p src/assets/styles
mkdir -p src/composables
mkdir -p src/utils
mkdir -p src/types
mkdir -p src/locales
```

### 第3步：迁移静态资源
```bash
# 复制图片资源
cp -r oldCode/uploads/* src/assets/images/
cp -r oldCode/dist/theme/static/imgs/* src/assets/images/

# 复制字体文件（如果有）
cp -r oldCode/dist/theme/static/fonts/* src/assets/fonts/
```

### 第4步：配置开发环境
1. **配置Vite**
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/assets/styles/variables.scss";`
      }
    }
  }
})
```

2. **配置TypeScript**
```json
// tsconfig.json 添加路径映射
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}
```

3. **配置ESLint和Prettier**
```json
// .eslintrc.cjs
module.exports = {
  extends: [
    'plugin:vue/vue3-essential',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier'
  ],
  rules: {
    'vue/multi-word-component-names': 'off'
  }
}
```

## 阶段二：基础组件开发（预计3-5天）

### 第5步：创建样式系统
1. **创建SCSS变量文件**
```scss
// src/assets/styles/variables.scss
:root {
  --theme-color: #3095fb;
  --text-color: #3c3c3c;
  --border-color: #ebedf0;
  --center-width: 1200px;
  --font-size: 16px;
}

$theme-color: #3095fb;
$text-color: #3c3c3c;
$border-color: #ebedf0;
$center-width: 1200px;

$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);
```

2. **创建工具类**
```scss
// src/assets/styles/utilities.scss
.lay-fx { display: flex; }
.lay-fd-r { flex-direction: row; }
.lay-fd-c { flex-direction: column; }
.lay-jc-c { justify-content: center; }
.lay-ai-c { align-items: center; }

// 间距工具类
@for $i from 1 through 10 {
  .m-t-#{$i * 5} { margin-top: #{$i * 5}px; }
  .m-b-#{$i * 5} { margin-bottom: #{$i * 5}px; }
  .p-t-#{$i * 5} { padding-top: #{$i * 5}px; }
  .p-b-#{$i * 5} { padding-bottom: #{$i * 5}px; }
}
```

3. **创建主样式文件**
```scss
// src/assets/styles/main.scss
@import 'variables';
@import 'mixins';
@import 'utilities';
@import 'base';
@import 'components';
```

### 第6步：开发基础布局组件

1. **AppRow.vue**
```vue
<template>
  <div class="app-row" :class="rowClass">
    <slot />
  </div>
</template>

<script setup lang="ts">
interface RowProps {
  justify?: 'start' | 'center' | 'end' | 'space-between' | 'space-around'
  align?: 'top' | 'middle' | 'bottom' | 'stretch'
  noGutters?: boolean
}

const props = withDefaults(defineProps<RowProps>(), {
  justify: 'start',
  align: 'top',
  noGutters: false
})

const rowClass = computed(() => [
  'cc-row',
  'cc-row--flex',
  `cc-row--justify__${props.justify}`,
  `cc-row--align__${props.align}`,
  { 'cc-row--no-gutters': props.noGutters }
])
</script>
```

2. **AppCol.vue**
```vue
<template>
  <div class="app-col" :class="colClass">
    <slot />
  </div>
</template>

<script setup lang="ts">
interface ColProps {
  span?: number
  xs?: number
  sm?: number
  md?: number
  lg?: number
  xl?: number
}

const props = defineProps<ColProps>()

const colClass = computed(() => [
  'cc-col',
  props.span && `cc-col-${props.span}`,
  props.xs && `cc-col-xs-${props.xs}`,
  props.sm && `cc-col-sm-${props.sm}`,
  props.md && `cc-col-md-${props.md}`,
  props.lg && `cc-col-lg-${props.lg}`,
  props.xl && `cc-col-xl-${props.xl}`
])
</script>
```

### 第7步：开发基础UI组件

1. **AppButton.vue**
```vue
<template>
  <component
    :is="tag"
    class="app-button cc-button"
    :class="buttonClass"
    :disabled="disabled || loading"
    :href="href"
    :target="target"
    @click="handleClick"
  >
    <i v-if="loading" class="loading-spinner"></i>
    <i v-else-if="icon" :class="icon"></i>
    <span class="button-text">
      <slot>{{ text }}</slot>
    </span>
  </component>
</template>

<script setup lang="ts">
interface ButtonProps {
  type?: 'primary' | 'secondary' | 'outline' | 'text'
  size?: 'small' | 'medium' | 'large'
  text?: string
  icon?: string
  loading?: boolean
  disabled?: boolean
  block?: boolean
  href?: string
  target?: string
}

const props = withDefaults(defineProps<ButtonProps>(), {
  type: 'primary',
  size: 'medium'
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const tag = computed(() => props.href ? 'a' : 'button')

const buttonClass = computed(() => [
  `cc-button--${props.type}`,
  `cc-button--${props.size}`,
  {
    'cc-button--loading': props.loading,
    'cc-button--disabled': props.disabled,
    'cc-button--block': props.block
  }
])

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>
```

### 第8步：开发布局组件

1. **AppLayout.vue**
```vue
<template>
  <div class="app-layout">
    <AppHeader v-if="showHeader" />
    <main class="app-main">
      <slot />
    </main>
    <AppFooter v-if="showFooter" />
  </div>
</template>

<script setup lang="ts">
interface LayoutProps {
  showHeader?: boolean
  showFooter?: boolean
}

withDefaults(defineProps<LayoutProps>(), {
  showHeader: true,
  showFooter: true
})
</script>
```

2. **AppHeader.vue**
```vue
<template>
  <header class="app-header">
    <!-- 顶部栏 -->
    <div class="header-topbar">
      <div class="container">
        <AppRow justify="space-between" align="middle">
          <AppCol>
            <div class="welcome-message">
              <i class="fas fa-home"></i>
              {{ $t('welcome.message') }}
            </div>
          </AppCol>
          <AppCol>
            <div class="topbar-actions">
              <LanguageSwitch />
              <SearchBox />
              <LoginButton />
            </div>
          </AppCol>
        </AppRow>
      </div>
    </div>

    <!-- 主导航 -->
    <div class="header-main">
      <div class="container">
        <AppRow justify="space-between" align="middle">
          <AppCol>
            <AppLogo />
          </AppCol>
          <AppCol>
            <AppNavigation :items="menuItems" />
          </AppCol>
        </AppRow>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
// 菜单配置
const menuItems = [
  { id: 'home', label: '首頁', path: '/' },
  {
    id: 'solutions',
    label: '解決方案',
    children: [
      { id: 'tourism', label: '智慧文旅', path: '/solutions/tourism' },
      { id: 'city', label: '智慧城市', path: '/solutions/city' },
      { id: 'ecology', label: '智慧生態', path: '/solutions/ecology' }
    ]
  },
  { id: 'insights', label: '行業洞察', path: '/insights' },
  { id: 'contact', label: '聯繫我們', path: '/contact' }
]
</script>
```