# 官网项目Vue 3重构规划文档

## 1. 项目概述

### 1.1 项目背景
- **项目名称**: 數字起源(香港)有限公司官网
- **当前技术**: 基于传统HTML/CSS/JS的静态网站
- **重构目标**: 迁移至Vue 3现代化前端框架
- **项目规模**: 多页面企业官网，包含中英文双语版本

### 1.2 重构目标
- 提升开发效率和代码可维护性
- 实现组件化开发，提高代码复用性
- 优化用户体验和页面性能
- 建立现代化的前端开发工作流

## 2. 旧版项目分析

### 2.1 项目结构分析
```
oldCode/
├── index.html                 # 主页（中文）
├── en.html                   # 英文主页
├── 关于我们.html              # 关于我们页面
├── 智慧文旅.html              # 解决方案页面
├── 行业洞察.html              # 行业洞察页面
├── dist/                     # 静态资源目录
│   ├── theme/static/         # 主题样式和脚本
│   │   ├── css/             # CSS文件
│   │   ├── js/              # JavaScript文件
│   │   └── imgs/            # 图片资源
│   └── visual/              # 可视化相关资源
├── uploads/                  # 上传文件目录
├── static/                   # 静态资源
├── 智慧城市/                 # 解决方案子页面
├── 智慧文旅/                 # 文旅解决方案子页面
├── 智慧生态/                 # 生态解决方案子页面
├── 数科云/                   # 数科云产品页面
└── ai应用/                   # AI应用页面
```

### 2.2 技术栈分析

#### 2.2.1 前端技术
- **HTML**: 传统HTML5结构，使用了大量内联样式和脚本
- **CSS**:
  - 使用CSS3特性，包含响应式设计
  - 大量使用Flexbox布局
  - 自定义CSS变量系统
  - FontAwesome图标库
- **JavaScript**:
  - 原生JavaScript + jQuery
  - 组件化系统（自定义useComponent函数）
  - 模块化加载机制

#### 2.2.2 页面组件分析
基于HTML结构分析，识别出以下主要组件：

1. **页面布局组件**
   - `Page-header`: 页面头部
   - `Page-body`: 页面主体
   - `Page-footer`: 页面底部
   - `Page-sidebar`: 侧边栏

2. **头部组件**
   - `Page-header--topbar`: 顶部栏（欢迎信息、语言切换）
   - `Page-header--main`: 主导航区域
   - `Page-header--logo`: Logo区域
   - `Page-header--menu`: 导航菜单
   - `Page-header--widgets`: 头部工具区（搜索、登录）

3. **内容组件**
   - `cc-row`: 行布局组件
   - `cc-col`: 列布局组件
   - `cc-textblock`: 文本块组件
   - `cc-button`: 按钮组件
   - `cc-picture`: 图片组件
   - `cc-menudropdown`: 下拉菜单组件
   - `cc-search`: 搜索组件

### 2.3 页面功能模块

#### 2.3.1 核心功能
1. **多语言支持**: 中文/英文切换
2. **响应式布局**: 支持PC和移动端
3. **导航系统**: 多级下拉菜单
4. **搜索功能**: 全站搜索
5. **用户系统**: 登录/注册入口

#### 2.3.2 业务模块
1. **首页展示**: 公司介绍、产品展示
2. **解决方案**: 智慧文旅、智慧城市、智慧生态等
3. **产品服务**: 数科云、AI应用等
4. **行业洞察**: 新闻资讯展示
5. **关于我们**: 公司信息、联系方式

### 2.4 样式系统分析

#### 2.4.1 CSS架构
- **CSS变量系统**: 定义了完整的设计令牌
- **工具类**: 大量的原子化CSS类
- **组件样式**: 每个组件都有对应的样式定义
- **响应式设计**: 使用媒体查询适配不同屏幕

#### 2.4.2 设计系统
```css
:root {
  --theme-color: #3095fb;
  --text-color: #3c3c3c;
  --border-color: #ebedf0;
  --center-width: 1200px;
  --font-size: 16px;
}
```

## 3. Vue 3重构方案

### 3.1 技术选型

#### 3.1.1 核心技术栈
- **Vue 3**: 使用Composition API
- **TypeScript**: 提供类型安全
- **Vite**: 构建工具
- **Vue Router 4**: 路由管理
- **Pinia**: 状态管理
- **Element Plus**: UI组件库（可选）

#### 3.1.2 开发工具
- **ESLint + Prettier**: 代码规范
- **Husky**: Git钩子
- **Commitizen**: 提交规范
- **Sass/SCSS**: CSS预处理器

### 3.2 项目架构设计

#### 3.2.1 目录结构
```
vue3-website/
├── public/                   # 静态资源
├── src/
│   ├── assets/              # 资源文件
│   │   ├── images/          # 图片
│   │   ├── styles/          # 样式文件
│   │   └── fonts/           # 字体文件
│   ├── components/          # 公共组件
│   │   ├── common/          # 通用组件
│   │   ├── layout/          # 布局组件
│   │   └── business/        # 业务组件
│   ├── views/               # 页面组件
│   │   ├── Home/            # 首页
│   │   ├── About/           # 关于我们
│   │   ├── Solutions/       # 解决方案
│   │   ├── Products/        # 产品服务
│   │   └── News/            # 行业洞察
│   ├── router/              # 路由配置
│   ├── stores/              # 状态管理
│   ├── composables/         # 组合式函数
│   ├── utils/               # 工具函数
│   ├── types/               # TypeScript类型定义
│   └── locales/             # 国际化文件
├── tests/                   # 测试文件
└── docs/                    # 文档
```

#### 3.2.2 组件设计原则
1. **单一职责**: 每个组件只负责一个功能
2. **可复用性**: 设计通用的基础组件
3. **可组合性**: 支持组件的灵活组合
4. **类型安全**: 使用TypeScript定义组件接口

### 3.3 核心组件设计

#### 3.3.1 布局组件
```typescript
// Layout/AppLayout.vue
interface LayoutProps {
  showHeader?: boolean;
  showFooter?: boolean;
  showSidebar?: boolean;
}
```

#### 3.3.2 头部组件
```typescript
// Layout/AppHeader.vue
interface HeaderProps {
  logo?: string;
  menuItems: MenuItem[];
  showSearch?: boolean;
  showLanguageSwitch?: boolean;
}
```

#### 3.3.3 导航组件
```typescript
// Navigation/AppNavigation.vue
interface MenuItem {
  id: string;
  label: string;
  path?: string;
  children?: MenuItem[];
  external?: boolean;
}
```

## 4. 实施计划

### 4.1 阶段划分

#### 第一阶段：项目初始化（1-2天）
- [ ] 创建Vue 3项目脚手架
- [ ] 配置开发环境和构建工具
- [ ] 建立代码规范和工作流
- [ ] 迁移静态资源

#### 第二阶段：基础组件开发（3-5天）
- [ ] 开发布局组件系统
- [ ] 实现头部和导航组件
- [ ] 创建基础UI组件库
- [ ] 建立样式系统

#### 第三阶段：页面重构（5-8天）
- [ ] 重构首页
- [ ] 重构关于我们页面
- [ ] 重构解决方案页面
- [ ] 重构产品服务页面
- [ ] 重构行业洞察页面

#### 第四阶段：功能完善（2-3天）
- [ ] 实现多语言支持
- [ ] 添加搜索功能
- [ ] 优化响应式设计
- [ ] 性能优化

#### 第五阶段：测试和部署（1-2天）
- [ ] 功能测试
- [ ] 兼容性测试
- [ ] 性能测试
- [ ] 部署配置

### 4.2 技术难点和解决方案

#### 4.2.1 多语言支持
**难点**: 原项目通过不同HTML文件实现多语言
**解决方案**: 使用Vue I18n实现动态语言切换

#### 4.2.2 SEO优化
**难点**: SPA应用的SEO问题
**解决方案**: 使用Nuxt.js或实现SSR

#### 4.2.3 样式迁移
**难点**: 大量的CSS样式需要重新组织
**解决方案**:
- 保留CSS变量系统
- 使用CSS Modules或Styled Components
- 建立设计令牌系统

#### 4.2.4 组件状态管理
**难点**: 原项目使用全局变量管理状态
**解决方案**: 使用Pinia进行状态管理

## 5. 风险评估和应对策略

### 5.1 技术风险
1. **兼容性问题**: 新技术栈可能存在兼容性问题
   - 应对策略: 充分测试，准备降级方案

2. **性能问题**: SPA可能影响首屏加载速度
   - 应对策略: 代码分割、懒加载、预加载

3. **SEO影响**: 单页应用对SEO不友好
   - 应对策略: 考虑SSR或预渲染

### 5.2 项目风险
1. **开发周期**: 重构可能超出预期时间
   - 应对策略: 分阶段实施，优先核心功能

2. **功能遗漏**: 可能遗漏原有功能
   - 应对策略: 详细的功能清单和测试用例

## 6. 后续优化建议

### 6.1 性能优化
- 实现代码分割和懒加载
- 优化图片资源（WebP格式、响应式图片）
- 使用CDN加速静态资源

### 6.2 用户体验优化
- 添加页面加载动画
- 实现平滑的页面切换效果
- 优化移动端交互体验

### 6.3 开发体验优化
- 建立组件文档系统
- 实现自动化测试
- 建立CI/CD流水线

## 7. 总结

本重构方案基于对现有项目的深入分析，采用Vue 3 + TypeScript的现代化技术栈，通过组件化开发提升代码质量和开发效率。重构过程将分阶段进行，确保项目的稳定性和可维护性。

重构完成后，项目将具备：
- 更好的代码组织和可维护性
- 更高的开发效率
- 更优的用户体验
- 更强的扩展性

这将为公司官网的长期发展奠定坚实的技术基础。