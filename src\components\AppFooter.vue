<template>
  <footer class="page-footer">
    <div class="container">
      <div class="footer-content">
        <div class="contact-info">
          <h3>聯繫方式</h3>
          <p>公司：數字起源 (香港) 有限公司</p>
          <p>地址：香港数码港道100号数码港3座C區6樓608-613室</p>
          <p>電話：00852-56147493</p>
          <p>郵箱：<EMAIL>；<EMAIL></p>
        </div>
        <div class="follow-us">
          <h3>關註我們</h3>
          <div class="social-links">
            <img src="/uploads/sites/1012/2022/11/14df9f2ffdcd56437226404d4d0993a1.jpg" alt="微信" />
            <img src="/uploads/sites/1012/2022/11/d9817bde75e239270021cd0e76b9e947.jpg" alt="公众号" />
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'AppFooter'
}
</script>

<style scoped>
/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面底部样式 */
.page-footer {
  background: #020e26;
  padding: 60px 0;
  color: #7b8595;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  gap: 60px;
}

.contact-info h3,
.follow-us h3 {
  color: #fff;
  font-size: 20px;
  margin-bottom: 20px;
}

.contact-info p {
  font-size: 14px;
  line-height: 1.6;
  margin: 8px 0;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links img {
  width: 80px;
  height: 80px;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 30px;
  }
}
</style>
