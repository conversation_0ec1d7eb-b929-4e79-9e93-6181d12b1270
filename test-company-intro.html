<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公司简介模块测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 公司简介模块 */
        .company-intro {
            padding: 80px 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            position: relative;
        }

        .company-intro::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('/uploads/sites/1012/2022/11/a28adbebd944acf1203439567578dc64.jpg') center/cover;
            opacity: 0.1;
            z-index: 1;
        }

        .intro-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .intro-header {
            margin-bottom: 40px;
        }

        .intro-subtitle {
            color: #0064d8;
            font-size: 16px;
            font-weight: 500;
            margin: 0 0 15px 0;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        .intro-title {
            color: #333;
            font-size: 36px;
            font-weight: bold;
            margin: 0 0 20px 0;
            line-height: 1.2;
        }

        .intro-divider {
            width: 100px;
            height: 6px;
            background: linear-gradient(90deg, #0064d8 0%, #667eea 100%);
            margin: 0 auto;
            border-radius: 3px;
        }

        .intro-description {
            text-align: justify;
        }

        .intro-text {
            color: #555;
            font-size: 18px;
            line-height: 2;
            margin: 0 0 30px 0;
            text-align: justify;
            text-justify: inter-ideograph;
        }

        .brand-slogan {
            color: #0064d8;
            font-size: 20px;
            font-weight: bold;
            margin: 0;
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border-left: 4px solid #0064d8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .company-intro {
                padding: 60px 0;
            }

            .intro-title {
                font-size: 28px;
            }

            .intro-text {
                font-size: 16px;
                line-height: 1.8;
                text-align: left;
            }

            .brand-slogan {
                font-size: 18px;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 公司简介模块 -->
    <section class="company-intro">
        <div class="container">
            <div class="intro-content">
                <div class="intro-header">
                    <p class="intro-subtitle">WHO WE ARE</p>
                    <h2 class="intro-title">數字起源 (香港) 有限公司</h2>
                    <div class="intro-divider"></div>
                </div>
                <div class="intro-description">
                    <p class="intro-text">
                        系環球數科股份有限公司（以下簡稱"集團"）下屬機構，作為集團海外總部、創新研發中心、全球運營中心，
                        公司將基於集團 20 餘年的行業沉澱及技術積累，依托香港在區位、政策、人才等方面的優勢，
                        聚焦前沿技術研究、技術驗證、產品預研及海外市場拓展，形成國內與海外市場雙循環的出海新格局。
                    </p>
                    <p class="brand-slogan">數字起源，連接無限可能，賦能美好生活。</p>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
